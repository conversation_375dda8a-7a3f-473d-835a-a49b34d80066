# Troubleshooting Guide

This guide helps resolve common issues when running the firewall automation debug tests.

## 🚨 Common Import Errors

### Error: `ModuleNotFoundError: No module named 'src.security.risk_analyzer'`

**Solution:**
```bash
# Run the import test first
python test_imports.py
```

If this fails, check:
1. You're in the correct directory (`firewall-automation/`)
2. Conda environment is activated: `conda activate firewall-automation`
3. All required files exist

### Error: `ModuleNotFoundError: No module named 'src'`

**Solution:**
```bash
# Make sure you're in the firewall-automation directory
cd firewall-automation

# Check if src directory exists
ls -la src/

# Run from the correct location
python debug_test_runner.py
```

### Error: Import errors with specific modules

**Solution:**
```bash
# Test imports step by step
python test_imports.py

# If specific modules fail, check file existence
ls -la src/security/
ls -la src/jira/
ls -la src/tufin/
```

## 🔧 Environment Issues

### Error: `conda: command not found`

**Solution:**
```bash
# Install Miniconda or Anaconda first
# Download from: https://docs.conda.io/en/latest/miniconda.html

# After installation, restart terminal and try:
conda --version
```

### Error: Environment not found

**Solution:**
```bash
# Create the environment
conda env create -f environment.yml

# Or create manually
conda create -n firewall-automation python=3.11
conda activate firewall-automation
conda install requests click urllib3 pandas jsonschema colorama pytest
```

### Error: Package not found in conda

**Solution:**
```bash
# Try installing from conda-forge
conda install -c conda-forge package-name

# Or use pip as fallback
pip install package-name
```

## 📁 File Structure Issues

### Missing files or directories

**Expected structure:**
```
firewall-automation/
├── src/
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py
│   │   └── security_standards.json
│   ├── jira/
│   │   ├── __init__.py
│   │   ├── client.py
│   │   └── parser.py
│   ├── security/
│   │   ├── __init__.py
│   │   ├── validator.py
│   │   └── risk_analyzer.py
│   ├── tufin/
│   │   ├── __init__.py
│   │   ├── client.py
│   │   └── analyzer.py
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   └── helpers.py
│   ├── main.py
│   └── workflow.py
├── tests/
│   ├── __init__.py
│   ├── mock_clients.py
│   ├── mock_data.py
│   └── test_security_validator.py
├── debug_test_runner.py
├── interactive_debug.py
├── quick_test.py
├── test_imports.py
└── environment.yml
```

**Solution:**
If files are missing, re-create them or check if you have the complete project.

## 🧪 Test Execution Issues

### Error: Tests fail with validation errors

**Solution:**
```bash
# Run individual test components
python test_imports.py          # Test imports first
python quick_test.py           # Test basic functionality
python debug_test_runner.py --test env  # Test environment only
```

### Error: Security standards not loading

**Solution:**
```bash
# Check if security standards file exists
ls -la src/config/security_standards.json

# Test security validator directly
python -c "
from src.security.validator import SecurityValidator
v = SecurityValidator()
print('Standards loaded:', v.standards is not None)
"
```

### Error: Mock clients not working

**Solution:**
```bash
# Test mock clients separately
python -c "
from tests.mock_clients import MockJiraClient, MockTufinClient
jira = MockJiraClient()
tufin = MockTufinClient()
print('Mock clients created successfully')
"
```

## 🐍 Python Path Issues

### Error: Python can't find modules

**Solution:**
```bash
# Check Python path
python -c "import sys; print('\n'.join(sys.path))"

# Run with explicit path
PYTHONPATH=./src:./tests python debug_test_runner.py

# Or use the test script which sets paths automatically
python test_imports.py
```

## 💻 Platform-Specific Issues

### Windows Issues

**Common problems:**
- Path separators (`\` vs `/`)
- PowerShell execution policy
- Conda not in PATH

**Solutions:**
```powershell
# Fix execution policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Add conda to PATH or use Anaconda Prompt
# Run from Anaconda Prompt instead of regular Command Prompt
```

### Linux/Mac Issues

**Common problems:**
- Permission issues
- Shell environment

**Solutions:**
```bash
# Make scripts executable
chmod +x debug_test_runner.py
chmod +x interactive_debug.py

# Use correct Python
which python
python --version
```

## 🔍 Debugging Steps

### Step 1: Basic Environment Check
```bash
# Check you're in the right place
pwd
ls -la

# Check Python and conda
python --version
conda --version
conda env list
```

### Step 2: Test Imports
```bash
# Run the import test
python test_imports.py

# If it fails, check each import manually
python -c "from src.config.settings import settings; print('Config OK')"
python -c "from src.security.validator import SecurityValidator; print('Security OK')"
```

### Step 3: Test Components
```bash
# Test individual components
python quick_test.py

# Test specific debug features
python debug_test_runner.py --test env
python debug_test_runner.py --test clients
```

### Step 4: Full Debug Run
```bash
# Run full debug suite
python debug_test_runner.py

# Or interactive mode
python interactive_debug.py
```

## 📞 Getting Help

If you're still having issues:

1. **Check the error message carefully** - it usually tells you exactly what's wrong
2. **Run `python test_imports.py`** - this will identify most common issues
3. **Verify file structure** - make sure all files exist in the right places
4. **Check conda environment** - make sure it's activated and has required packages
5. **Try the quick test** - `python quick_test.py` for basic functionality

## 🎯 Quick Fix Commands

```bash
# Complete reset and setup
conda deactivate
conda env remove -n firewall-automation
conda env create -f environment.yml
conda activate firewall-automation
python test_imports.py
python debug_test_runner.py
```

## ✅ Success Indicators

You know everything is working when:
- ✅ `python test_imports.py` passes all tests
- ✅ `python quick_test.py` shows validation results
- ✅ `python debug_test_runner.py` runs without import errors
- ✅ `python interactive_debug.py` starts the menu system

Once these work, you can test the full system with mock data without needing actual Jira/Tufin connectivity!
