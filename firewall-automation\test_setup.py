#!/usr/bin/env python3
"""
Test script to validate the firewall automation setup.
"""

import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all modules can be imported."""
    print("🔍 Testing module imports...")
    
    try:
        from src.config.settings import settings
        print("✅ Config module imported successfully")
        
        from src.jira.client import JiraClient
        from src.jira.parser import FirewallTableParser
        print("✅ Jira modules imported successfully")
        
        from src.security.validator import SecurityValidator
        print("✅ Security modules imported successfully")
        
        from src.tufin.client import TufinClient
        from src.tufin.analyzer import TufinAnalyzer
        print("✅ Tufin modules imported successfully")
        
        from src.utils.logger import setup_logging, get_logger
        from src.utils.helpers import validate_ip_address, parse_port_range
        print("✅ Utility modules imported successfully")
        
        from src.workflow import FirewallAnalysisWorkflow
        print("✅ Workflow module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("\n🔧 Testing configuration...")
    
    try:
        from src.config.settings import settings
        
        # Test that settings object exists
        assert hasattr(settings, 'jira')
        assert hasattr(settings, 'tufin')
        assert hasattr(settings, 'app')
        print("✅ Settings structure is correct")
        
        # Test security standards file exists
        standards_file = Path(settings.app.security_standards_file)
        if standards_file.exists():
            print("✅ Security standards file found")
        else:
            print("⚠️  Security standards file not found (this is expected in test environment)")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


def test_security_standards():
    """Test security standards loading."""
    print("\n🔒 Testing security standards...")
    
    try:
        from src.security.validator import SecurityValidator
        
        # Create validator (will use default standards file)
        validator = SecurityValidator()
        
        if validator.standards:
            print("✅ Security standards loaded successfully")
            print(f"   Version: {validator.standards.get('version', 'unknown')}")
            print(f"   Prohibited sources: {len(validator.standards.get('prohibited_sources', {}).get('wildcards', []))}")
            print(f"   Risky protocols: {len(validator.standards.get('risky_protocols', {}).get('high_risk', []))}")
        else:
            print("⚠️  Security standards file not loaded (expected in test environment)")
        
        return True
        
    except Exception as e:
        print(f"❌ Security standards error: {e}")
        return False


def test_utilities():
    """Test utility functions."""
    print("\n🛠️  Testing utilities...")
    
    try:
        from src.utils.helpers import validate_ip_address, parse_port_range
        
        # Test IP validation
        assert validate_ip_address("***********") == True
        assert validate_ip_address("***********/24") == True
        assert validate_ip_address("invalid") == False
        print("✅ IP address validation working")
        
        # Test port parsing
        assert parse_port_range("80") == (80, 80)
        assert parse_port_range("80-443") == (80, 443)
        assert parse_port_range("1000 to 2000") == (1000, 2000)
        print("✅ Port range parsing working")
        
        return True
        
    except Exception as e:
        print(f"❌ Utilities error: {e}")
        return False


def test_logging():
    """Test logging setup."""
    print("\n📝 Testing logging...")
    
    try:
        from src.utils.logger import setup_logging, get_logger
        
        # Setup logging
        logger = setup_logging(log_level="INFO", log_format="text")
        
        # Test logger
        test_logger = get_logger("test")
        test_logger.info("Test log message")
        
        print("✅ Logging setup successful")
        return True
        
    except Exception as e:
        print(f"❌ Logging error: {e}")
        return False


def test_sample_parsing():
    """Test sample firewall rule parsing."""
    print("\n📊 Testing sample parsing...")
    
    try:
        from src.jira.parser import FirewallTableParser, FirewallRule
        
        parser = FirewallTableParser()
        
        # Sample issue data
        sample_issue = {
            'key': 'TEST-123',
            'summary': 'Test firewall change',
            'description': '''
            Firewall Change Request:
            
            ||Field||Value||
            |Source|***********/24|
            |Destination|**********|
            |Port|443|
            |Protocol|HTTPS|
            |Action|Allow|
            |Justification|Business application access|
            '''
        }
        
        # Parse the issue
        request = parser.parse_issue(sample_issue)
        
        print(f"✅ Parsed {len(request.rules)} rules from sample issue")
        if request.rules:
            rule = request.rules[0]
            print(f"   Source: {rule.source}")
            print(f"   Destination: {rule.destination}")
            print(f"   Port: {rule.port}")
            print(f"   Protocol: {rule.protocol}")
        
        if request.parsing_errors:
            print(f"⚠️  Parsing errors: {request.parsing_errors}")
        
        return True
        
    except Exception as e:
        print(f"❌ Parsing error: {e}")
        return False


def test_sample_validation():
    """Test sample rule validation."""
    print("\n🔍 Testing sample validation...")
    
    try:
        from src.security.validator import SecurityValidator
        from src.jira.parser import FirewallRule
        
        validator = SecurityValidator()
        
        # Test compliant rule
        good_rule = FirewallRule(
            source="***********00",
            destination="**********",
            port="443",
            protocol="HTTPS",
            justification="Business application access"
        )
        
        result = validator.validate_rule(good_rule)
        print(f"✅ Good rule validation: {result.is_compliant} (risk score: {result.total_risk_score})")
        
        # Test non-compliant rule
        bad_rule = FirewallRule(
            source="*",
            destination="any",
            port="22",
            protocol="SSH"
        )
        
        result = validator.validate_rule(bad_rule)
        print(f"✅ Bad rule validation: {result.is_compliant} (risk score: {result.total_risk_score})")
        print(f"   Violations found: {len(result.violations)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Firewall Automation Setup Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_security_standards,
        test_utilities,
        test_logging,
        test_sample_parsing,
        test_sample_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Setup is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
