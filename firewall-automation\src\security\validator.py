"""
Security standards validator for firewall change requests.
"""

import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import re

from ..jira.parser import FirewallRule, FirewallChangeRequest
from ..config.settings import settings
from ..utils.logger import get_logger


class ViolationType(Enum):
    """Types of security violations."""
    WILDCARD_SOURCE = "wildcard_source"
    WILDCARD_DESTINATION = "wildcard_destination"
    CRITICAL_PROTOCOL = "critical_protocol"
    RISKY_PROTOCOL = "risky_protocol"
    LEGACY_PROTOCOL = "legacy_protocol"
    PROHIBITED_PORT = "prohibited_port"
    CRITICAL_INFRASTRUCTURE_PORT = "critical_infrastructure_port"
    DATABASE_PORT = "database_port"
    MANAGEMENT_PORT = "management_port"
    REMOTE_ACCESS_PORT = "remote_access_port"
    SUSPICIOUS_PORT = "suspicious_port"
    P2P_PORT = "p2p_port"
    LARGE_PORT_RANGE = "large_port_range"
    SUSPICIOUS_RANGE = "suspicious_range"
    MISSING_JUSTIFICATION = "missing_justification"
    WEAK_JUSTIFICATION = "weak_justification"
    RED_FLAG_JUSTIFICATION = "red_flag_justification"
    RFC1918_EXTERNAL = "rfc1918_external"
    MULTICAST_BROADCAST = "multicast_broadcast"
    UNTRUSTED_SOURCE = "untrusted_source"
    CRITICAL_DESTINATION = "critical_destination"
    COMPLIANCE_VIOLATION = "compliance_violation"
    HIGH_RISK_TIME = "high_risk_time"


class Severity(Enum):
    """Severity levels for violations."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Violation:
    """Represents a security standards violation."""
    type: ViolationType
    severity: Severity
    message: str
    field: str
    value: str
    recommendation: str
    risk_score: int


@dataclass
class ValidationResult:
    """Result of security validation."""
    rule: FirewallRule
    violations: List[Violation]
    total_risk_score: int
    is_compliant: bool
    recommendations: List[str]


class SecurityValidator:
    """Validates firewall rules against security standards."""

    def __init__(self, standards_file: Optional[str] = None):
        self.logger = get_logger(__name__)
        self.standards_file = standards_file or settings.app.security_standards_file
        self.standards = self._load_security_standards()

    def _load_security_standards(self) -> Dict[str, Any]:
        """Load security standards from JSON file."""
        try:
            with open(self.standards_file, 'r') as f:
                standards = json.load(f)

            self.logger.info(
                "Loaded security standards",
                file=self.standards_file,
                version=standards.get('version', 'unknown')
            )
            return standards

        except Exception as e:
            self.logger.error(
                "Failed to load security standards",
                file=self.standards_file,
                error=str(e)
            )
            return {}

    def validate_request(self, request: FirewallChangeRequest) -> List[ValidationResult]:
        """
        Validate all rules in a firewall change request.

        Args:
            request: FirewallChangeRequest to validate

        Returns:
            List of ValidationResult objects
        """
        results = []

        self.logger.info(
            "Validating firewall change request",
            issue_key=request.issue_key,
            rules_count=len(request.rules)
        )

        for rule in request.rules:
            result = self.validate_rule(rule)
            results.append(result)

        total_violations = sum(len(r.violations) for r in results)
        total_risk_score = sum(r.total_risk_score for r in results)

        self.logger.info(
            "Completed validation",
            issue_key=request.issue_key,
            total_violations=total_violations,
            total_risk_score=total_risk_score
        )

        return results

    def validate_rule(self, rule: FirewallRule) -> ValidationResult:
        """
        Validate a single firewall rule.

        Args:
            rule: FirewallRule to validate

        Returns:
            ValidationResult object
        """
        violations = []

        # Check source address
        source_violations = self._check_source_address(rule.source)
        violations.extend(source_violations)

        # Check destination address
        dest_violations = self._check_destination_address(rule.destination)
        violations.extend(dest_violations)

        # Check protocol
        protocol_violations = self._check_protocol(rule.protocol)
        violations.extend(protocol_violations)

        # Check port
        port_violations = self._check_port(rule.port)
        violations.extend(port_violations)

        # Check action
        action_violations = self._check_action(rule.action)
        violations.extend(action_violations)

        # Check justification
        justification_violations = self._check_justification(rule.justification)
        violations.extend(justification_violations)

        # Check for suspicious patterns
        suspicious_violations = self._check_suspicious_patterns(rule)
        violations.extend(suspicious_violations)

        # Check compliance requirements
        compliance_violations = self._check_compliance(rule)
        violations.extend(compliance_violations)

        # Check network security patterns
        network_violations = self._check_network_security(rule)
        violations.extend(network_violations)

        # Calculate total risk score
        total_risk_score = sum(v.risk_score for v in violations)

        # Determine compliance
        is_compliant = len(violations) == 0

        # Generate recommendations
        recommendations = [v.recommendation for v in violations if v.recommendation]

        return ValidationResult(
            rule=rule,
            violations=violations,
            total_risk_score=total_risk_score,
            is_compliant=is_compliant,
            recommendations=recommendations
        )

    def _check_source_address(self, source: str) -> List[Violation]:
        """Check source address for violations."""
        violations = []

        # Check for wildcard sources
        prohibited_wildcards = self.standards.get('prohibited_sources', {}).get('wildcards', [])
        if source.lower() in [w.lower() for w in prohibited_wildcards]:
            violations.append(Violation(
                type=ViolationType.WILDCARD_SOURCE,
                severity=Severity.HIGH,
                message=f"Source address '{source}' uses prohibited wildcard",
                field="source",
                value=source,
                recommendation="Specify exact source IP addresses or subnets instead of wildcards",
                risk_score=self.standards.get('risk_scoring', {}).get('wildcard_source', 10)
            ))

        # Check for untrusted networks
        untrusted_networks = self.standards.get('prohibited_sources', {}).get('untrusted_networks', [])
        if source.lower() in [n.lower() for n in untrusted_networks]:
            violations.append(Violation(
                type=ViolationType.UNTRUSTED_SOURCE,
                severity=Severity.HIGH,
                message=f"Source '{source}' is from an untrusted network",
                field="source",
                value=source,
                recommendation="Avoid allowing access from untrusted networks",
                risk_score=self.standards.get('risk_scoring', {}).get('wildcard_source', 10)
            ))

        return violations

    def _check_destination_address(self, destination: str) -> List[Violation]:
        """Check destination address for violations."""
        violations = []

        # Check for wildcard destinations
        prohibited_wildcards = self.standards.get('prohibited_destinations', {}).get('wildcards', [])
        if destination.lower() in [w.lower() for w in prohibited_wildcards]:
            violations.append(Violation(
                type=ViolationType.WILDCARD_DESTINATION,
                severity=Severity.HIGH,
                message=f"Destination address '{destination}' uses prohibited wildcard",
                field="destination",
                value=destination,
                recommendation="Specify exact destination IP addresses or subnets instead of wildcards",
                risk_score=self.standards.get('risk_scoring', {}).get('wildcard_destination', 9)
            ))

        # Check for critical internal destinations
        critical_internal = self.standards.get('prohibited_destinations', {}).get('critical_internal', [])
        if destination.lower() in [c.lower() for c in critical_internal]:
            violations.append(Violation(
                type=ViolationType.CRITICAL_DESTINATION,
                severity=Severity.CRITICAL,
                message=f"Destination '{destination}' is critical infrastructure",
                field="destination",
                value=destination,
                recommendation="Access to critical infrastructure requires special approval and justification",
                risk_score=self.standards.get('risk_scoring', {}).get('critical_infrastructure_port', 9)
            ))

        return violations

    def _check_protocol(self, protocol: str) -> List[Violation]:
        """Check protocol for violations."""
        violations = []

        if not protocol:
            return violations

        protocol_lower = protocol.lower()

        # Check critical risk protocols
        critical_risk = self.standards.get('risky_protocols', {}).get('critical_risk', [])
        if protocol_lower in [p.lower() for p in critical_risk]:
            violations.append(Violation(
                type=ViolationType.CRITICAL_PROTOCOL,
                severity=Severity.CRITICAL,
                message=f"Protocol '{protocol}' is prohibited due to critical security risks",
                field="protocol",
                value=protocol,
                recommendation="Use secure alternatives immediately - this protocol should not be used",
                risk_score=self.standards.get('risk_scoring', {}).get('critical_protocol', 10)
            ))

        # Check high-risk protocols
        high_risk = self.standards.get('risky_protocols', {}).get('high_risk', [])
        if protocol_lower in [p.lower() for p in high_risk]:
            violations.append(Violation(
                type=ViolationType.RISKY_PROTOCOL,
                severity=Severity.HIGH,
                message=f"Protocol '{protocol}' is considered high-risk",
                field="protocol",
                value=protocol,
                recommendation="Use secure alternatives or provide strong business justification",
                risk_score=self.standards.get('risk_scoring', {}).get('risky_protocol_high', 8)
            ))

        # Check medium-risk protocols
        medium_risk = self.standards.get('risky_protocols', {}).get('medium_risk', [])
        if protocol_lower in [p.lower() for p in medium_risk]:
            violations.append(Violation(
                type=ViolationType.RISKY_PROTOCOL,
                severity=Severity.MEDIUM,
                message=f"Protocol '{protocol}' requires additional security considerations",
                field="protocol",
                value=protocol,
                recommendation="Consider using encrypted alternatives (HTTPS instead of HTTP)",
                risk_score=self.standards.get('risk_scoring', {}).get('risky_protocol_medium', 5)
            ))

        # Check legacy protocols
        legacy_protocols = self.standards.get('risky_protocols', {}).get('legacy_protocols', [])
        if protocol_lower in [p.lower() for p in legacy_protocols]:
            violations.append(Violation(
                type=ViolationType.LEGACY_PROTOCOL,
                severity=Severity.MEDIUM,
                message=f"Protocol '{protocol}' is legacy and should be phased out",
                field="protocol",
                value=protocol,
                recommendation="Plan migration to modern protocols",
                risk_score=self.standards.get('risk_scoring', {}).get('legacy_protocol', 7)
            ))

        return violations

    def _check_port(self, port: str) -> List[Violation]:
        """Check port for violations."""
        violations = []

        if not port:
            return violations

        # Check prohibited ports
        prohibited = self.standards.get('dangerous_ports', {}).get('prohibited', [])
        if port in prohibited:
            violations.append(Violation(
                type=ViolationType.PROHIBITED_PORT,
                severity=Severity.CRITICAL,
                message=f"Port '{port}' is prohibited",
                field="port",
                value=port,
                recommendation="Use alternative secure ports or protocols",
                risk_score=self.standards.get('risk_scoring', {}).get('prohibited_port', 10)
            ))

        # Check critical infrastructure ports
        critical_infra = self.standards.get('dangerous_ports', {}).get('critical_infrastructure', [])
        if port in critical_infra:
            violations.append(Violation(
                type=ViolationType.CRITICAL_INFRASTRUCTURE_PORT,
                severity=Severity.HIGH,
                message=f"Port '{port}' is used by critical infrastructure",
                field="port",
                value=port,
                recommendation="Access to critical infrastructure ports requires special approval",
                risk_score=self.standards.get('risk_scoring', {}).get('critical_infrastructure_port', 9)
            ))

        # Check database ports
        database_ports = self.standards.get('dangerous_ports', {}).get('database_ports', [])
        if port in database_ports:
            violations.append(Violation(
                type=ViolationType.DATABASE_PORT,
                severity=Severity.HIGH,
                message=f"Port '{port}' is a database port",
                field="port",
                value=port,
                recommendation="Database access should be restricted and properly secured",
                risk_score=self.standards.get('risk_scoring', {}).get('database_port', 8)
            ))

        # Check management ports
        management_ports = self.standards.get('dangerous_ports', {}).get('management_ports', [])
        if port in management_ports:
            violations.append(Violation(
                type=ViolationType.MANAGEMENT_PORT,
                severity=Severity.MEDIUM,
                message=f"Port '{port}' is a management port",
                field="port",
                value=port,
                recommendation="Management ports should have restricted access",
                risk_score=self.standards.get('risk_scoring', {}).get('management_port', 7)
            ))

        # Check remote access ports
        remote_access = self.standards.get('dangerous_ports', {}).get('remote_access', [])
        if port in remote_access:
            violations.append(Violation(
                type=ViolationType.REMOTE_ACCESS_PORT,
                severity=Severity.MEDIUM,
                message=f"Port '{port}' provides remote access",
                field="port",
                value=port,
                recommendation="Remote access should be secured with strong authentication",
                risk_score=self.standards.get('risk_scoring', {}).get('remote_access_port', 6)
            ))

        # Check ports requiring justification
        requires_justification = self.standards.get('dangerous_ports', {}).get('requires_justification', [])
        if port in requires_justification:
            violations.append(Violation(
                type=ViolationType.MANAGEMENT_PORT,
                severity=Severity.MEDIUM,
                message=f"Port '{port}' requires business justification",
                field="port",
                value=port,
                recommendation="Provide detailed business justification for using this port",
                risk_score=self.standards.get('risk_scoring', {}).get('management_port', 7)
            ))

        # Check port ranges
        if '-' in port or 'to' in port.lower():
            range_size = self._calculate_port_range_size(port)
            max_range = self.standards.get('port_ranges', {}).get('max_range_size', 50)

            if range_size > max_range:
                violations.append(Violation(
                    type=ViolationType.LARGE_PORT_RANGE,
                    severity=Severity.MEDIUM,
                    message=f"Port range '{port}' is too large ({range_size} ports)",
                    field="port",
                    value=port,
                    recommendation=f"Limit port ranges to {max_range} ports or less",
                    risk_score=self.standards.get('risk_scoring', {}).get('large_port_range', 6)
                ))

            # Check for suspicious ranges
            suspicious_ranges = self.standards.get('port_ranges', {}).get('suspicious_ranges', [])
            if port in suspicious_ranges:
                violations.append(Violation(
                    type=ViolationType.SUSPICIOUS_RANGE,
                    severity=Severity.MEDIUM,
                    message=f"Port range '{port}' is commonly associated with suspicious activity",
                    field="port",
                    value=port,
                    recommendation="Verify legitimate business need for this port range",
                    risk_score=self.standards.get('risk_scoring', {}).get('suspicious_range', 5)
                ))

        return violations

    def _check_action(self, action: str) -> List[Violation]:
        """Check action for violations."""
        violations = []

        # Currently no specific action violations defined
        # This can be extended based on requirements

        return violations

    def _check_justification(self, justification: Optional[str]) -> List[Violation]:
        """Check business justification for violations."""
        violations = []

        require_justification = self.standards.get('validation_rules', {}).get('require_business_justification', True)
        min_length = self.standards.get('validation_rules', {}).get('min_justification_length', 20)

        if require_justification and not justification:
            violations.append(Violation(
                type=ViolationType.MISSING_JUSTIFICATION,
                severity=Severity.MEDIUM,
                message="Business justification is required but missing",
                field="justification",
                value="",
                recommendation="Provide detailed business justification for this firewall change",
                risk_score=self.standards.get('risk_scoring', {}).get('missing_justification', 4)
            ))
        elif justification:
            # Check justification length
            if len(justification) < min_length:
                violations.append(Violation(
                    type=ViolationType.WEAK_JUSTIFICATION,
                    severity=Severity.LOW,
                    message=f"Justification is too brief ({len(justification)} characters, minimum {min_length})",
                    field="justification",
                    value=justification,
                    recommendation="Provide more detailed business justification",
                    risk_score=self.standards.get('risk_scoring', {}).get('weak_justification', 3)
                ))

            # Check for red flag keywords
            red_flags = self.standards.get('justification_keywords', {}).get('red_flags', [])
            justification_lower = justification.lower()

            for red_flag in red_flags:
                if red_flag.lower() in justification_lower:
                    violations.append(Violation(
                        type=ViolationType.RED_FLAG_JUSTIFICATION,
                        severity=Severity.MEDIUM,
                        message=f"Justification contains concerning keyword: '{red_flag}'",
                        field="justification",
                        value=justification,
                        recommendation="Provide proper business justification instead of temporary workarounds",
                        risk_score=self.standards.get('risk_scoring', {}).get('weak_justification', 3)
                    ))

        return violations

    def _check_suspicious_patterns(self, rule: FirewallRule) -> List[Violation]:
        """Check for suspicious patterns in the firewall rule."""
        violations = []

        # Check for common attack ports
        attack_ports = self.standards.get('suspicious_patterns', {}).get('common_attack_ports', [])
        if rule.port in attack_ports:
            violations.append(Violation(
                type=ViolationType.SUSPICIOUS_PORT,
                severity=Severity.HIGH,
                message=f"Port '{rule.port}' is commonly used in attacks",
                field="port",
                value=rule.port,
                recommendation="Verify legitimate business need and implement additional security measures",
                risk_score=self.standards.get('risk_scoring', {}).get('suspicious_port', 8)
            ))

        # Check for P2P ports
        p2p_ports = self.standards.get('suspicious_patterns', {}).get('p2p_ports', [])
        if rule.port in p2p_ports:
            violations.append(Violation(
                type=ViolationType.P2P_PORT,
                severity=Severity.MEDIUM,
                message=f"Port '{rule.port}' is commonly used for P2P applications",
                field="port",
                value=rule.port,
                recommendation="P2P applications may violate corporate policy",
                risk_score=self.standards.get('risk_scoring', {}).get('p2p_port', 7)
            ))

        # Check for Tor ports
        tor_ports = self.standards.get('suspicious_patterns', {}).get('tor_ports', [])
        if rule.port in tor_ports:
            violations.append(Violation(
                type=ViolationType.SUSPICIOUS_PORT,
                severity=Severity.HIGH,
                message=f"Port '{rule.port}' is associated with Tor network",
                field="port",
                value=rule.port,
                recommendation="Tor usage may violate corporate policy and security standards",
                risk_score=self.standards.get('risk_scoring', {}).get('suspicious_port', 8)
            ))

        # Check for cryptocurrency ports
        bitcoin_ports = self.standards.get('suspicious_patterns', {}).get('bitcoin_ports', [])
        if rule.port in bitcoin_ports:
            violations.append(Violation(
                type=ViolationType.SUSPICIOUS_PORT,
                severity=Severity.MEDIUM,
                message=f"Port '{rule.port}' is used for cryptocurrency applications",
                field="port",
                value=rule.port,
                recommendation="Cryptocurrency applications may violate corporate policy",
                risk_score=self.standards.get('risk_scoring', {}).get('suspicious_port', 8)
            ))

        return violations

    def _check_compliance(self, rule: FirewallRule) -> List[Violation]:
        """Check for compliance framework violations."""
        violations = []

        # PCI DSS compliance checks
        pci_prohibited = self.standards.get('compliance_frameworks', {}).get('pci_dss', {}).get('prohibited_protocols', [])
        if rule.protocol.lower() in [p.lower() for p in pci_prohibited]:
            violations.append(Violation(
                type=ViolationType.COMPLIANCE_VIOLATION,
                severity=Severity.HIGH,
                message=f"Protocol '{rule.protocol}' violates PCI DSS requirements",
                field="protocol",
                value=rule.protocol,
                recommendation="Use PCI DSS compliant protocols with proper encryption",
                risk_score=self.standards.get('risk_scoring', {}).get('risky_protocol_high', 8)
            ))

        return violations

    def _check_network_security(self, rule: FirewallRule) -> List[Violation]:
        """Check for network security violations."""
        violations = []

        # Check for RFC1918 external routing
        rfc1918_networks = self.standards.get('network_security', {}).get('rfc1918_external', {}).get('networks', [])
        for network in rfc1918_networks:
            if network in rule.source or network in rule.destination:
                violations.append(Violation(
                    type=ViolationType.RFC1918_EXTERNAL,
                    severity=Severity.MEDIUM,
                    message=f"RFC1918 private network detected: {network}",
                    field="source/destination",
                    value=f"{rule.source} -> {rule.destination}",
                    recommendation="Private networks should not be routed externally",
                    risk_score=self.standards.get('risk_scoring', {}).get('rfc1918_external', 8)
                ))

        # Check for multicast/broadcast addresses
        multicast_broadcast = self.standards.get('network_security', {}).get('multicast_broadcast', {}).get('addresses', [])
        if rule.destination in multicast_broadcast:
            violations.append(Violation(
                type=ViolationType.MULTICAST_BROADCAST,
                severity=Severity.MEDIUM,
                message=f"Destination '{rule.destination}' is multicast/broadcast address",
                field="destination",
                value=rule.destination,
                recommendation="Review need for multicast/broadcast traffic",
                risk_score=self.standards.get('risk_scoring', {}).get('multicast_broadcast', 6)
            ))

        return violations

    def _calculate_port_range_size(self, port_range: str) -> int:
        """Calculate the size of a port range."""
        try:
            # Handle different range formats
            if '-' in port_range:
                start, end = port_range.split('-', 1)
                return int(end.strip()) - int(start.strip()) + 1
            elif 'to' in port_range.lower():
                parts = port_range.lower().split('to')
                if len(parts) == 2:
                    start, end = parts
                    return int(end.strip()) - int(start.strip()) + 1

            # Single port
            return 1

        except (ValueError, IndexError):
            # If we can't parse it, assume it's large
            return 1000
