"""
Security standards validator for firewall change requests.
"""

import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import re
import structlog

from ..jira.parser import FirewallRule, FirewallChangeRequest
from ..config.settings import settings
from ..utils.logger import get_logger


class ViolationType(Enum):
    """Types of security violations."""
    WILDCARD_SOURCE = "wildcard_source"
    WILDCARD_DESTINATION = "wildcard_destination"
    RISKY_PROTOCOL = "risky_protocol"
    DANGEROUS_PORT = "dangerous_port"
    LARGE_PORT_RANGE = "large_port_range"
    MISSING_JUSTIFICATION = "missing_justification"
    PROHIBITED_ACTION = "prohibited_action"


class Severity(Enum):
    """Severity levels for violations."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Violation:
    """Represents a security standards violation."""
    type: ViolationType
    severity: Severity
    message: str
    field: str
    value: str
    recommendation: str
    risk_score: int


@dataclass
class ValidationResult:
    """Result of security validation."""
    rule: FirewallRule
    violations: List[Violation]
    total_risk_score: int
    is_compliant: bool
    recommendations: List[str]


class SecurityValidator:
    """Validates firewall rules against security standards."""
    
    def __init__(self, standards_file: Optional[str] = None):
        self.logger = get_logger(__name__)
        self.standards_file = standards_file or settings.app.security_standards_file
        self.standards = self._load_security_standards()
    
    def _load_security_standards(self) -> Dict[str, Any]:
        """Load security standards from JSON file."""
        try:
            with open(self.standards_file, 'r') as f:
                standards = json.load(f)
            
            self.logger.info(
                "Loaded security standards",
                file=self.standards_file,
                version=standards.get('version', 'unknown')
            )
            return standards
            
        except Exception as e:
            self.logger.error(
                "Failed to load security standards",
                file=self.standards_file,
                error=str(e)
            )
            return {}
    
    def validate_request(self, request: FirewallChangeRequest) -> List[ValidationResult]:
        """
        Validate all rules in a firewall change request.
        
        Args:
            request: FirewallChangeRequest to validate
        
        Returns:
            List of ValidationResult objects
        """
        results = []
        
        self.logger.info(
            "Validating firewall change request",
            issue_key=request.issue_key,
            rules_count=len(request.rules)
        )
        
        for rule in request.rules:
            result = self.validate_rule(rule)
            results.append(result)
        
        total_violations = sum(len(r.violations) for r in results)
        total_risk_score = sum(r.total_risk_score for r in results)
        
        self.logger.info(
            "Completed validation",
            issue_key=request.issue_key,
            total_violations=total_violations,
            total_risk_score=total_risk_score
        )
        
        return results
    
    def validate_rule(self, rule: FirewallRule) -> ValidationResult:
        """
        Validate a single firewall rule.
        
        Args:
            rule: FirewallRule to validate
        
        Returns:
            ValidationResult object
        """
        violations = []
        
        # Check source address
        source_violations = self._check_source_address(rule.source)
        violations.extend(source_violations)
        
        # Check destination address
        dest_violations = self._check_destination_address(rule.destination)
        violations.extend(dest_violations)
        
        # Check protocol
        protocol_violations = self._check_protocol(rule.protocol)
        violations.extend(protocol_violations)
        
        # Check port
        port_violations = self._check_port(rule.port)
        violations.extend(port_violations)
        
        # Check action
        action_violations = self._check_action(rule.action)
        violations.extend(action_violations)
        
        # Check justification
        justification_violations = self._check_justification(rule.justification)
        violations.extend(justification_violations)
        
        # Calculate total risk score
        total_risk_score = sum(v.risk_score for v in violations)
        
        # Determine compliance
        is_compliant = len(violations) == 0
        
        # Generate recommendations
        recommendations = [v.recommendation for v in violations if v.recommendation]
        
        return ValidationResult(
            rule=rule,
            violations=violations,
            total_risk_score=total_risk_score,
            is_compliant=is_compliant,
            recommendations=recommendations
        )
    
    def _check_source_address(self, source: str) -> List[Violation]:
        """Check source address for violations."""
        violations = []
        
        prohibited = self.standards.get('prohibited_sources', {}).get('wildcards', [])
        
        for wildcard in prohibited:
            if source.lower() in [w.lower() for w in prohibited]:
                violations.append(Violation(
                    type=ViolationType.WILDCARD_SOURCE,
                    severity=Severity.HIGH,
                    message=f"Source address '{source}' uses prohibited wildcard",
                    field="source",
                    value=source,
                    recommendation="Specify exact source IP addresses or subnets instead of wildcards",
                    risk_score=self.standards.get('risk_scoring', {}).get('wildcard_source', 10)
                ))
                break
        
        return violations
    
    def _check_destination_address(self, destination: str) -> List[Violation]:
        """Check destination address for violations."""
        violations = []
        
        prohibited = self.standards.get('prohibited_destinations', {}).get('wildcards', [])
        
        for wildcard in prohibited:
            if destination.lower() in [w.lower() for w in prohibited]:
                violations.append(Violation(
                    type=ViolationType.WILDCARD_DESTINATION,
                    severity=Severity.HIGH,
                    message=f"Destination address '{destination}' uses prohibited wildcard",
                    field="destination",
                    value=destination,
                    recommendation="Specify exact destination IP addresses or subnets instead of wildcards",
                    risk_score=self.standards.get('risk_scoring', {}).get('wildcard_destination', 8)
                ))
                break
        
        return violations
    
    def _check_protocol(self, protocol: str) -> List[Violation]:
        """Check protocol for violations."""
        violations = []
        
        if not protocol:
            return violations
        
        protocol_lower = protocol.lower()
        
        # Check high-risk protocols
        high_risk = self.standards.get('risky_protocols', {}).get('high_risk', [])
        if protocol_lower in [p.lower() for p in high_risk]:
            violations.append(Violation(
                type=ViolationType.RISKY_PROTOCOL,
                severity=Severity.HIGH,
                message=f"Protocol '{protocol}' is considered high-risk",
                field="protocol",
                value=protocol,
                recommendation="Use secure alternatives or provide strong business justification",
                risk_score=self.standards.get('risk_scoring', {}).get('risky_protocol_high', 9)
            ))
        
        # Check medium-risk protocols
        medium_risk = self.standards.get('risky_protocols', {}).get('medium_risk', [])
        if protocol_lower in [p.lower() for p in medium_risk]:
            violations.append(Violation(
                type=ViolationType.RISKY_PROTOCOL,
                severity=Severity.MEDIUM,
                message=f"Protocol '{protocol}' requires additional security considerations",
                field="protocol",
                value=protocol,
                recommendation="Consider using encrypted alternatives (HTTPS instead of HTTP)",
                risk_score=self.standards.get('risk_scoring', {}).get('risky_protocol_medium', 5)
            ))
        
        return violations
    
    def _check_port(self, port: str) -> List[Violation]:
        """Check port for violations."""
        violations = []
        
        if not port:
            return violations
        
        # Check prohibited ports
        prohibited = self.standards.get('dangerous_ports', {}).get('prohibited', [])
        if port in prohibited:
            violations.append(Violation(
                type=ViolationType.DANGEROUS_PORT,
                severity=Severity.CRITICAL,
                message=f"Port '{port}' is prohibited",
                field="port",
                value=port,
                recommendation="Use alternative secure ports or protocols",
                risk_score=self.standards.get('risk_scoring', {}).get('dangerous_port', 7)
            ))
        
        # Check ports requiring justification
        requires_justification = self.standards.get('dangerous_ports', {}).get('requires_justification', [])
        if port in requires_justification:
            violations.append(Violation(
                type=ViolationType.DANGEROUS_PORT,
                severity=Severity.MEDIUM,
                message=f"Port '{port}' requires business justification",
                field="port",
                value=port,
                recommendation="Provide detailed business justification for using this port",
                risk_score=self.standards.get('risk_scoring', {}).get('dangerous_port', 7)
            ))
        
        # Check port ranges
        if '-' in port or 'to' in port.lower():
            range_size = self._calculate_port_range_size(port)
            max_range = self.standards.get('port_ranges', {}).get('max_range_size', 100)
            
            if range_size > max_range:
                violations.append(Violation(
                    type=ViolationType.LARGE_PORT_RANGE,
                    severity=Severity.MEDIUM,
                    message=f"Port range '{port}' is too large ({range_size} ports)",
                    field="port",
                    value=port,
                    recommendation=f"Limit port ranges to {max_range} ports or less",
                    risk_score=self.standards.get('risk_scoring', {}).get('large_port_range', 6)
                ))
        
        return violations
    
    def _check_action(self, action: str) -> List[Violation]:
        """Check action for violations."""
        violations = []
        
        # Currently no specific action violations defined
        # This can be extended based on requirements
        
        return violations
    
    def _check_justification(self, justification: Optional[str]) -> List[Violation]:
        """Check business justification for violations."""
        violations = []
        
        require_justification = self.standards.get('validation_rules', {}).get('require_business_justification', True)
        
        if require_justification and not justification:
            violations.append(Violation(
                type=ViolationType.MISSING_JUSTIFICATION,
                severity=Severity.MEDIUM,
                message="Business justification is required but missing",
                field="justification",
                value="",
                recommendation="Provide detailed business justification for this firewall change",
                risk_score=self.standards.get('risk_scoring', {}).get('missing_justification', 4)
            ))
        
        return violations
    
    def _calculate_port_range_size(self, port_range: str) -> int:
        """Calculate the size of a port range."""
        try:
            # Handle different range formats
            if '-' in port_range:
                start, end = port_range.split('-', 1)
                return int(end.strip()) - int(start.strip()) + 1
            elif 'to' in port_range.lower():
                parts = port_range.lower().split('to')
                if len(parts) == 2:
                    start, end = parts
                    return int(end.strip()) - int(start.strip()) + 1
            
            # Single port
            return 1
            
        except (ValueError, IndexError):
            # If we can't parse it, assume it's large
            return 1000
