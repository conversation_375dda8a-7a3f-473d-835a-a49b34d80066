"""
Tests for security validator module.
"""

import pytest
import json
import tempfile
from pathlib import Path

from src.security.validator import SecurityValidator, ViolationType, Severity
from src.jira.parser import FirewallRule


@pytest.fixture
def sample_security_standards():
    """Sample security standards for testing."""
    return {
        "version": "1.0",
        "prohibited_sources": {
            "wildcards": ["*", "any", "0.0.0.0/0"]
        },
        "prohibited_destinations": {
            "wildcards": ["*", "any", "0.0.0.0/0"]
        },
        "risky_protocols": {
            "critical_risk": ["telnet", "rsh", "rlogin"],
            "high_risk": ["ssh", "ftp", "snmp"],
            "medium_risk": ["http", "pop3"],
            "legacy_protocols": ["netbeui", "ipx"]
        },
        "dangerous_ports": {
            "prohibited": ["23", "69"],
            "critical_infrastructure": ["53", "88", "389"],
            "database_ports": ["1433", "3306", "5432"],
            "management_ports": ["161", "162", "8080"],
            "remote_access": ["3389", "5900"],
            "requires_justification": ["22", "21", "80"]
        },
        "suspicious_patterns": {
            "common_attack_ports": ["4444", "31337", "12345"],
            "p2p_ports": ["6881", "6882"],
            "tor_ports": ["9001", "9050"],
            "bitcoin_ports": ["8333"]
        },
        "port_ranges": {
            "max_range_size": 100
        },
        "validation_rules": {
            "require_business_justification": True,
            "min_justification_length": 20
        },
        "justification_keywords": {
            "red_flags": ["testing", "temporary", "quick_fix"]
        },
        "risk_scoring": {
            "wildcard_source": 10,
            "wildcard_destination": 9,
            "critical_protocol": 10,
            "risky_protocol_high": 8,
            "risky_protocol_medium": 5,
            "legacy_protocol": 7,
            "prohibited_port": 10,
            "critical_infrastructure_port": 9,
            "database_port": 8,
            "management_port": 7,
            "remote_access_port": 6,
            "suspicious_port": 8,
            "p2p_port": 7,
            "large_port_range": 6,
            "missing_justification": 4,
            "weak_justification": 3
        }
    }


@pytest.fixture
def validator_with_standards(sample_security_standards):
    """Create validator with temporary standards file."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_security_standards, f)
        standards_file = f.name

    validator = SecurityValidator(standards_file)
    yield validator

    # Cleanup
    Path(standards_file).unlink()


class TestSecurityValidator:
    """Test cases for SecurityValidator."""

    def test_wildcard_source_violation(self, validator_with_standards):
        """Test detection of wildcard source addresses."""
        rule = FirewallRule(
            source="*",
            destination="**********",
            port="443",
            protocol="HTTPS"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        assert len(result.violations) >= 1

        wildcard_violations = [
            v for v in result.violations
            if v.type == ViolationType.WILDCARD_SOURCE
        ]
        assert len(wildcard_violations) == 1
        assert wildcard_violations[0].severity == Severity.HIGH

    def test_wildcard_destination_violation(self, validator_with_standards):
        """Test detection of wildcard destination addresses."""
        rule = FirewallRule(
            source="*************",
            destination="any",
            port="443",
            protocol="HTTPS"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        wildcard_violations = [
            v for v in result.violations
            if v.type == ViolationType.WILDCARD_DESTINATION
        ]
        assert len(wildcard_violations) == 1

    def test_risky_protocol_high(self, validator_with_standards):
        """Test detection of high-risk protocols."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="22",
            protocol="ssh"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        protocol_violations = [
            v for v in result.violations
            if v.type == ViolationType.RISKY_PROTOCOL
        ]
        assert len(protocol_violations) == 1
        assert protocol_violations[0].severity == Severity.HIGH

    def test_risky_protocol_medium(self, validator_with_standards):
        """Test detection of medium-risk protocols."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="80",
            protocol="http"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        protocol_violations = [
            v for v in result.violations
            if v.type == ViolationType.RISKY_PROTOCOL
        ]
        assert len(protocol_violations) == 1
        assert protocol_violations[0].severity == Severity.MEDIUM

    def test_dangerous_port_prohibited(self, validator_with_standards):
        """Test detection of prohibited ports."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="23",  # Telnet - prohibited
            protocol="TCP"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        port_violations = [
            v for v in result.violations
            if v.type == ViolationType.PROHIBITED_PORT
        ]
        assert len(port_violations) == 1
        assert port_violations[0].severity == Severity.CRITICAL

    def test_dangerous_port_requires_justification(self, validator_with_standards):
        """Test detection of ports requiring justification."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="22",  # SSH - requires justification
            protocol="TCP"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        port_violations = [
            v for v in result.violations
            if v.type == ViolationType.MANAGEMENT_PORT
        ]
        assert len(port_violations) == 1
        assert port_violations[0].severity == Severity.MEDIUM

    def test_missing_justification(self, validator_with_standards):
        """Test detection of missing business justification."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="443",
            protocol="HTTPS",
            justification=None  # Missing justification
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        justification_violations = [
            v for v in result.violations
            if v.type == ViolationType.MISSING_JUSTIFICATION
        ]
        assert len(justification_violations) == 1

    def test_compliant_rule(self, validator_with_standards):
        """Test a compliant firewall rule."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="443",
            protocol="HTTPS",
            justification="Business application access"
        )

        result = validator_with_standards.validate_rule(rule)

        assert result.is_compliant
        assert len(result.violations) == 0
        assert result.total_risk_score == 0

    def test_risk_score_calculation(self, validator_with_standards):
        """Test risk score calculation."""
        rule = FirewallRule(
            source="*",  # Wildcard source (10 points)
            destination="**********",
            port="22",  # SSH port requiring justification (7 points)
            protocol="ssh",  # High-risk protocol (9 points)
            justification=None  # Missing justification (4 points)
        )

        result = validator_with_standards.validate_rule(rule)

        # Should have multiple violations
        assert not result.is_compliant
        assert len(result.violations) >= 3

        # Risk score should be sum of individual violations
        # Note: Actual score may vary based on new risk categories
        assert result.total_risk_score > 20  # Should be high risk

    def test_port_range_validation(self, validator_with_standards):
        """Test port range size validation."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="1000-2000",  # Range of 1001 ports (exceeds limit of 100)
            protocol="TCP",
            justification="Large port range needed"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        range_violations = [
            v for v in result.violations
            if v.type == ViolationType.LARGE_PORT_RANGE
        ]
        assert len(range_violations) == 1

    def test_critical_protocol_violation(self, validator_with_standards):
        """Test detection of critical risk protocols."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="23",
            protocol="telnet",  # Critical risk protocol
            justification="Emergency access needed"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        critical_violations = [
            v for v in result.violations
            if v.type == ViolationType.CRITICAL_PROTOCOL
        ]
        assert len(critical_violations) == 1
        assert critical_violations[0].severity == Severity.CRITICAL

    def test_suspicious_port_detection(self, validator_with_standards):
        """Test detection of suspicious ports."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="31337",  # Common attack port
            protocol="TCP",
            justification="Business requirement for application"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        suspicious_violations = [
            v for v in result.violations
            if v.type == ViolationType.SUSPICIOUS_PORT
        ]
        assert len(suspicious_violations) == 1

    def test_database_port_detection(self, validator_with_standards):
        """Test detection of database ports."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="1433",  # SQL Server port
            protocol="TCP",
            justification="Application database connection"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        db_violations = [
            v for v in result.violations
            if v.type == ViolationType.DATABASE_PORT
        ]
        assert len(db_violations) == 1
        assert db_violations[0].severity == Severity.HIGH

    def test_weak_justification_detection(self, validator_with_standards):
        """Test detection of weak justifications."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="443",
            protocol="HTTPS",
            justification="testing"  # Too short and red flag keyword
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        justification_violations = [
            v for v in result.violations
            if v.type in [ViolationType.WEAK_JUSTIFICATION, ViolationType.RED_FLAG_JUSTIFICATION]
        ]
        assert len(justification_violations) >= 1

    def test_p2p_port_detection(self, validator_with_standards):
        """Test detection of P2P ports."""
        rule = FirewallRule(
            source="*************",
            destination="**********",
            port="6881",  # BitTorrent port
            protocol="TCP",
            justification="File sharing for business purposes"
        )

        result = validator_with_standards.validate_rule(rule)

        assert not result.is_compliant
        p2p_violations = [
            v for v in result.violations
            if v.type == ViolationType.P2P_PORT
        ]
        assert len(p2p_violations) == 1
        assert p2p_violations[0].severity == Severity.MEDIUM


if __name__ == '__main__':
    pytest.main([__file__])
