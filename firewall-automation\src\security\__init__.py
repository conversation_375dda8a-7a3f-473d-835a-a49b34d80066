"""
Security validation module for firewall change automation.
"""

from .validator import SecurityValidator, ValidationResult

# Import risk analyzer with error handling for optional dependency
try:
    from .risk_analyzer import RiskAnalyzer, RiskAssessment
    __all__ = ['SecurityValidator', 'ValidationResult', 'RiskAnalyzer', 'RiskAssessment']
except ImportError:
    # Risk analyzer is optional
    __all__ = ['SecurityValidator', 'ValidationResult']
