"""
Tufin API client for querying firewall rules and policies.
"""

import requests
from typing import List, Dict, Any, Optional
import json
from requests.auth import HTTPBasicAuth

from ..config.settings import settings
from ..utils.logger import get_logger


class TufinClient:
    """Client for interacting with Tufin SecureTrack API."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.tufin_settings = settings.tufin
        self.session = requests.Session()
        self.session.verify = self.tufin_settings.verify_ssl
        self.session.timeout = self.tufin_settings.timeout
        self._authenticated = False

    def authenticate(self) -> bool:
        """
        Authenticate with Tufin API.

        Returns:
            True if authentication successful, False otherwise
        """
        try:
            self.session.auth = HTTPBasicAuth(
                self.tufin_settings.username,
                self.tufin_settings.password
            )

            # Test authentication with a simple API call
            response = self.session.get(
                f"{self.tufin_settings.url}/securetrack/api/devices"
            )

            if response.status_code == 200:
                self._authenticated = True
                self.logger.info(
                    "Successfully authenticated with <PERSON><PERSON>",
                    server=self.tufin_settings.url
                )
                return True
            else:
                self.logger.error(
                    "Failed to authenticate with <PERSON><PERSON>",
                    status_code=response.status_code,
                    response=response.text
                )
                return False

        except Exception as e:
            self.logger.error(
                "Error during Tufin authentication",
                error=str(e)
            )
            return False

    def get_devices(self) -> List[Dict[str, Any]]:
        """
        Get list of firewall devices.

        Returns:
            List of device information dictionaries
        """
        if not self._authenticated and not self.authenticate():
            return []

        try:
            response = self.session.get(
                f"{self.tufin_settings.url}/securetrack/api/devices"
            )

            if response.status_code == 200:
                devices = response.json()
                self.logger.info(
                    "Retrieved devices from Tufin",
                    device_count=len(devices)
                )
                return devices
            else:
                self.logger.error(
                    "Failed to retrieve devices",
                    status_code=response.status_code
                )
                return []

        except Exception as e:
            self.logger.error(
                "Error retrieving devices",
                error=str(e)
            )
            return []

    def search_rules(
        self,
        source: Optional[str] = None,
        destination: Optional[str] = None,
        port: Optional[str] = None,
        protocol: Optional[str] = None,
        device_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for firewall rules matching criteria.

        Args:
            source: Source IP or network
            destination: Destination IP or network
            port: Port number or range
            protocol: Protocol (TCP, UDP, etc.)
            device_id: Specific device ID to search

        Returns:
            List of matching rules
        """
        if not self._authenticated and not self.authenticate():
            return []

        # Build search parameters
        params = {}
        if source:
            params['source'] = source
        if destination:
            params['destination'] = destination
        if port:
            params['service'] = port
        if protocol:
            params['protocol'] = protocol
        if device_id:
            params['device'] = device_id

        try:
            response = self.session.get(
                f"{self.tufin_settings.url}/securetrack/api/rules/search",
                params=params
            )

            if response.status_code == 200:
                rules = response.json()
                self.logger.info(
                    "Found matching rules",
                    rule_count=len(rules),
                    search_params=params
                )
                return rules
            else:
                self.logger.error(
                    "Failed to search rules",
                    status_code=response.status_code,
                    params=params
                )
                return []

        except Exception as e:
            self.logger.error(
                "Error searching rules",
                error=str(e),
                params=params
            )
            return []

    def get_rule_details(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific rule.

        Args:
            rule_id: Rule identifier

        Returns:
            Rule details dictionary or None
        """
        if not self._authenticated and not self.authenticate():
            return None

        try:
            response = self.session.get(
                f"{self.tufin_settings.url}/securetrack/api/rules/{rule_id}"
            )

            if response.status_code == 200:
                rule_details = response.json()
                self.logger.info(
                    "Retrieved rule details",
                    rule_id=rule_id
                )
                return rule_details
            else:
                self.logger.error(
                    "Failed to retrieve rule details",
                    rule_id=rule_id,
                    status_code=response.status_code
                )
                return None

        except Exception as e:
            self.logger.error(
                "Error retrieving rule details",
                rule_id=rule_id,
                error=str(e)
            )
            return None

    def analyze_rule_impact(
        self,
        source: str,
        destination: str,
        port: str,
        protocol: str
    ) -> Dict[str, Any]:
        """
        Analyze the impact of a proposed rule change.

        Args:
            source: Source IP or network
            destination: Destination IP or network
            port: Port number or range
            protocol: Protocol

        Returns:
            Impact analysis results
        """
        if not self._authenticated and not self.authenticate():
            return {}

        # This would typically use Tufin's policy analysis APIs
        # For now, we'll simulate the analysis

        try:
            # Search for existing rules that might conflict
            existing_rules = self.search_rules(
                source=source,
                destination=destination,
                port=port,
                protocol=protocol
            )

            # Search for overlapping rules
            overlapping_rules = self.search_rules(
                destination=destination,
                port=port
            )

            analysis = {
                'existing_rules': existing_rules,
                'overlapping_rules': overlapping_rules,
                'conflicts': [],
                'recommendations': []
            }

            # Analyze conflicts
            if existing_rules:
                analysis['conflicts'].append({
                    'type': 'duplicate_rule',
                    'message': f"Found {len(existing_rules)} existing rules with similar criteria",
                    'severity': 'medium'
                })
                analysis['recommendations'].append(
                    "Review existing rules before creating new ones"
                )

            if len(overlapping_rules) > 5:
                analysis['conflicts'].append({
                    'type': 'too_many_overlapping_rules',
                    'message': f"Found {len(overlapping_rules)} overlapping rules",
                    'severity': 'low'
                })
                analysis['recommendations'].append(
                    "Consider consolidating overlapping rules"
                )

            self.logger.info(
                "Completed rule impact analysis",
                existing_rules=len(existing_rules),
                overlapping_rules=len(overlapping_rules),
                conflicts=len(analysis['conflicts'])
            )

            return analysis

        except Exception as e:
            self.logger.error(
                "Error during rule impact analysis",
                error=str(e)
            )
            return {}

    def get_policy_violations(self, rule_criteria: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        Check for policy violations in proposed rule.

        Args:
            rule_criteria: Dictionary with rule criteria

        Returns:
            List of policy violations
        """
        if not self._authenticated and not self.authenticate():
            return []

        # This would integrate with Tufin's policy compliance APIs
        # For now, we'll return a placeholder

        violations = []

        # Example policy checks
        if rule_criteria.get('source') in ['any', '*', '0.0.0.0/0']:
            violations.append({
                'type': 'overly_permissive_source',
                'severity': 'high',
                'message': 'Source address is too permissive',
                'recommendation': 'Specify exact source networks'
            })

        if rule_criteria.get('destination') in ['any', '*', '0.0.0.0/0']:
            violations.append({
                'type': 'overly_permissive_destination',
                'severity': 'high',
                'message': 'Destination address is too permissive',
                'recommendation': 'Specify exact destination networks'
            })

        return violations
