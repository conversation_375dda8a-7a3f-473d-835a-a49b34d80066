# Firewall Automation Environment Configuration
# Copy this file to .env and update with your actual values
# Make sure to activate the conda environment: conda activate firewall-automation

# Jira Configuration
JIRA_URL=https://your-company.atlassian.net
JIRA_USERNAME=<EMAIL>
JIRA_API_TOKEN=your-jira-api-token
JIRA_PROJECT_KEY=FW

# Tufin Configuration
TUFIN_URL=https://your-tufin-server.company.com
TUFIN_USERNAME=your-tufin-username
TUFIN_PASSWORD=your-tufin-password
TUFIN_VERIFY_SSL=true
TUFIN_TIMEOUT=30

# Application Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
OUTPUT_DIR=./output
SECURITY_STANDARDS_FILE=./src/config/security_standards.json
DRY_RUN=false
