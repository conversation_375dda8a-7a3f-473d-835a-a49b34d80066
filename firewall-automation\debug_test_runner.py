#!/usr/bin/env python3
"""
Debug test runner for firewall automation system.
Tests the complete workflow with mock data without external API dependencies.
"""

import sys
import os
import json
import tempfile
from pathlib import Path
from typing import Dict, Any, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "tests"))

from src.jira.parser import FirewallTableParser
from src.security.validator import SecurityValidator
from src.tufin.analyzer import TufinAnalyzer
from src.utils.logger import setup_logging, get_logger
from tests.mock_clients import MockJiraClient, MockTufinClient
from tests.mock_data import TEST_SCENARIOS, MockEnvironmentData


class DebugTestRunner:
    """Debug test runner for the firewall automation system."""
    
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.logger = setup_logging(log_level="DEBUG", log_format="text")
        self.results = {}
        
        # Initialize mock clients
        self.mock_jira = MockJiraClient()
        self.mock_tufin = MockTufinClient()
        
        # Initialize components
        self.parser = FirewallTableParser()
        self.validator = SecurityValidator()
        self.analyzer = TufinAnalyzer(self.mock_tufin)
    
    def print_header(self, title: str):
        """Print a formatted header."""
        if self.verbose:
            print(f"\n{'='*60}")
            print(f" {title}")
            print(f"{'='*60}")
    
    def print_section(self, title: str):
        """Print a formatted section header."""
        if self.verbose:
            print(f"\n{'-'*40}")
            print(f" {title}")
            print(f"{'-'*40}")
    
    def test_environment_setup(self) -> bool:
        """Test environment setup and configuration."""
        self.print_header("ENVIRONMENT SETUP TEST")
        
        try:
            # Test mock environment variables
            env_vars = MockEnvironmentData.get_test_env_vars()
            
            print("✅ Mock environment variables:")
            for key, value in env_vars.items():
                masked_value = "***" if "password" in key.lower() or "token" in key.lower() else value
                print(f"   {key}: {masked_value}")
            
            # Test logger setup
            test_logger = get_logger("test")
            test_logger.info("Test log message", test_param="test_value")
            print("✅ Logger setup successful")
            
            # Test security standards loading
            if self.validator.standards:
                print(f"✅ Security standards loaded (version: {self.validator.standards.get('version', 'unknown')})")
            else:
                print("⚠️  Security standards not loaded")
            
            return True
            
        except Exception as e:
            print(f"❌ Environment setup failed: {e}")
            return False
    
    def test_mock_clients(self) -> bool:
        """Test mock client functionality."""
        self.print_header("MOCK CLIENT TESTS")
        
        try:
            # Test Jira client
            self.print_section("Jira Client Test")
            
            jira_connected = self.mock_jira.connect()
            print(f"✅ Jira connection: {jira_connected}")
            
            # Test issue retrieval
            test_issue = self.mock_jira.get_issue('FW-123')
            if test_issue:
                print(f"✅ Issue retrieval: {test_issue['key']} - {test_issue['summary']}")
            else:
                print("❌ Issue retrieval failed")
            
            # Test comment addition
            comment_added = self.mock_jira.add_comment('FW-123', 'Test comment from debug runner')
            print(f"✅ Comment addition: {comment_added}")
            
            # Test Tufin client
            self.print_section("Tufin Client Test")
            
            tufin_auth = self.mock_tufin.authenticate()
            print(f"✅ Tufin authentication: {tufin_auth}")
            
            # Test device retrieval
            devices = self.mock_tufin.get_devices()
            print(f"✅ Device retrieval: {len(devices)} devices found")
            
            # Test rule search
            rules = self.mock_tufin.search_rules(destination='10.0.0.50', port='443')
            print(f"✅ Rule search: {len(rules)} rules found")
            
            return True
            
        except Exception as e:
            print(f"❌ Mock client test failed: {e}")
            return False
    
    def test_parsing(self) -> bool:
        """Test firewall rule parsing."""
        self.print_header("FIREWALL RULE PARSING TESTS")
        
        try:
            for scenario_name, scenario in TEST_SCENARIOS.items():
                self.print_section(f"Parsing Test: {scenario['name']}")
                
                # Parse the issue
                request = self.parser.parse_issue(scenario['jira_data'])
                
                print(f"Issue Key: {request.issue_key}")
                print(f"Rules Found: {len(request.rules)}")
                print(f"Parsing Errors: {len(request.parsing_errors)}")
                
                if request.parsing_errors:
                    print("Parsing Errors:")
                    for error in request.parsing_errors:
                        print(f"  - {error}")
                
                if request.rules:
                    for i, rule in enumerate(request.rules, 1):
                        print(f"Rule {i}:")
                        print(f"  Source: {rule.source}")
                        print(f"  Destination: {rule.destination}")
                        print(f"  Port: {rule.port}")
                        print(f"  Protocol: {rule.protocol}")
                        print(f"  Action: {rule.action}")
                        print(f"  Justification: {rule.justification[:50]}..." if rule.justification else "  Justification: None")
                
                print("✅ Parsing completed")
            
            return True
            
        except Exception as e:
            print(f"❌ Parsing test failed: {e}")
            return False
    
    def test_security_validation(self) -> bool:
        """Test security validation."""
        self.print_header("SECURITY VALIDATION TESTS")
        
        try:
            for scenario_name, scenario in TEST_SCENARIOS.items():
                self.print_section(f"Validation Test: {scenario['name']}")
                
                # Parse the issue first
                request = self.parser.parse_issue(scenario['jira_data'])
                
                if not request.rules:
                    print("⚠️  No rules to validate")
                    continue
                
                # Validate each rule
                total_violations = 0
                total_risk_score = 0
                
                for i, rule in enumerate(request.rules, 1):
                    print(f"\nRule {i} Validation:")
                    result = self.validator.validate_rule(rule)
                    
                    print(f"  Compliant: {result.is_compliant}")
                    print(f"  Risk Score: {result.total_risk_score}")
                    print(f"  Violations: {len(result.violations)}")
                    
                    if result.violations:
                        print("  Violation Details:")
                        for violation in result.violations:
                            print(f"    - {violation.type.value}: {violation.message} (Score: {violation.risk_score})")
                    
                    if result.recommendations:
                        print("  Recommendations:")
                        for rec in result.recommendations:
                            print(f"    - {rec}")
                    
                    total_violations += len(result.violations)
                    total_risk_score += result.total_risk_score
                
                print(f"\nTotal Violations: {total_violations}")
                print(f"Total Risk Score: {total_risk_score}")
                print(f"Expected Risk Score: {scenario['expected_risk_score']}")
                
                # Store results for summary
                self.results[scenario_name] = {
                    'violations': total_violations,
                    'risk_score': total_risk_score,
                    'expected_risk_score': scenario['expected_risk_score'],
                    'expected_violations': scenario['expected_violations']
                }
                
                print("✅ Validation completed")
            
            return True
            
        except Exception as e:
            print(f"❌ Security validation test failed: {e}")
            return False
    
    def test_tufin_analysis(self) -> bool:
        """Test Tufin rule analysis."""
        self.print_header("TUFIN ANALYSIS TESTS")
        
        try:
            for scenario_name, scenario in TEST_SCENARIOS.items():
                self.print_section(f"Tufin Analysis: {scenario['name']}")
                
                # Parse the issue first
                request = self.parser.parse_issue(scenario['jira_data'])
                
                if not request.rules:
                    print("⚠️  No rules to analyze")
                    continue
                
                # Analyze each rule
                for i, rule in enumerate(request.rules, 1):
                    print(f"\nRule {i} Analysis:")
                    analysis = self.analyzer.analyze_rule(rule)
                    
                    print(f"  Existing Rules: {len(analysis.existing_rules)}")
                    print(f"  Conflicts: {len(analysis.conflicts)}")
                    print(f"  Recommendations: {len(analysis.recommendations)}")
                    print(f"  Should Create New: {analysis.should_create_new}")
                    print(f"  Should Modify Existing: {analysis.should_modify_existing}")
                    
                    if analysis.conflicts:
                        print("  Conflict Details:")
                        for conflict in analysis.conflicts:
                            print(f"    - {conflict.type.value}: {conflict.message}")
                    
                    if analysis.recommendations:
                        print("  Recommendations:")
                        for rec in analysis.recommendations:
                            print(f"    - {rec}")
                
                print("✅ Tufin analysis completed")
            
            return True
            
        except Exception as e:
            print(f"❌ Tufin analysis test failed: {e}")
            return False
    
    def test_end_to_end_workflow(self) -> bool:
        """Test complete end-to-end workflow."""
        self.print_header("END-TO-END WORKFLOW TEST")
        
        try:
            # Test with one scenario
            scenario = TEST_SCENARIOS['high_risk']
            issue_key = scenario['jira_data']['key']
            
            print(f"Testing complete workflow for: {issue_key}")
            
            # Step 1: Retrieve issue
            self.print_section("Step 1: Retrieve Jira Issue")
            issue_data = self.mock_jira.get_issue(issue_key)
            print(f"✅ Retrieved issue: {issue_data['key']}")
            
            # Step 2: Parse firewall request
            self.print_section("Step 2: Parse Firewall Request")
            request = self.parser.parse_issue(issue_data)
            print(f"✅ Parsed {len(request.rules)} rules")
            
            # Step 3: Validate security standards
            self.print_section("Step 3: Security Validation")
            validation_results = []
            for rule in request.rules:
                result = self.validator.validate_rule(rule)
                validation_results.append(result)
            
            total_risk = sum(r.total_risk_score for r in validation_results)
            print(f"✅ Validation complete - Total risk score: {total_risk}")
            
            # Step 4: Tufin analysis
            self.print_section("Step 4: Tufin Analysis")
            tufin_results = []
            for rule in request.rules:
                analysis = self.analyzer.analyze_rule(rule)
                tufin_results.append(analysis)
            
            total_conflicts = sum(len(a.conflicts) for a in tufin_results)
            print(f"✅ Tufin analysis complete - {total_conflicts} conflicts found")
            
            # Step 5: Generate recommendations
            self.print_section("Step 5: Generate Recommendations")
            recommendations = []
            
            for result in validation_results:
                for violation in result.violations:
                    recommendations.append({
                        'title': f"{violation.type.value.replace('_', ' ').title()} Violation",
                        'description': violation.message,
                        'severity': violation.severity.value,
                        'category': 'security_standards'
                    })
            
            for analysis in tufin_results:
                for conflict in analysis.conflicts:
                    recommendations.append({
                        'title': f"Rule Conflict: {conflict.type.value.replace('_', ' ').title()}",
                        'description': conflict.message,
                        'severity': conflict.severity,
                        'category': 'rule_analysis'
                    })
            
            print(f"✅ Generated {len(recommendations)} recommendations")
            
            # Step 6: Post to Jira (mock)
            self.print_section("Step 6: Post to Jira")
            comment_body = self._format_jira_comment(recommendations, total_risk)
            comment_posted = self.mock_jira.add_comment(issue_key, comment_body)
            print(f"✅ Comment posted: {comment_posted}")
            
            print(f"\n🎉 End-to-end workflow completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ End-to-end workflow failed: {e}")
            return False
    
    def _format_jira_comment(self, recommendations: List[Dict], risk_score: int) -> str:
        """Format recommendations as Jira comment."""
        lines = [
            "🔒 *Automated Firewall Security Analysis*",
            "",
            f"📊 *Risk Score:* {risk_score}/10",
            ""
        ]
        
        if risk_score >= 8:
            lines.append("🚨 *HIGH RISK* - Immediate attention required")
        elif risk_score >= 6:
            lines.append("⚠️ *MEDIUM RISK* - Review recommended")
        else:
            lines.append("ℹ️ *LOW RISK* - Minor issues identified")
        
        lines.extend(["", "🔍 *Security Analysis Results:*", ""])
        
        if not recommendations:
            lines.append("✅ No security issues identified.")
        else:
            for i, rec in enumerate(recommendations, 1):
                severity_emoji = {
                    'critical': '🚨',
                    'high': '⚠️',
                    'medium': '⚡',
                    'low': 'ℹ️'
                }.get(rec.get('severity', 'info'), 'ℹ️')
                
                lines.extend([
                    f"{i}. {severity_emoji} *{rec.get('title', 'Unknown Issue')}*",
                    f"   {rec.get('description', 'No description available')}",
                    ""
                ])
        
        return "\n".join(lines)
    
    def print_summary(self):
        """Print test summary."""
        self.print_header("TEST SUMMARY")
        
        if not self.results:
            print("No test results to summarize.")
            return
        
        print(f"{'Scenario':<20} {'Risk Score':<12} {'Expected':<10} {'Violations':<12} {'Status':<10}")
        print("-" * 70)
        
        for scenario_name, result in self.results.items():
            actual_risk = result['risk_score']
            expected_risk = result['expected_risk_score']
            violations = result['violations']
            
            # Simple pass/fail based on risk score being in reasonable range
            status = "✅ PASS" if abs(actual_risk - expected_risk) <= 5 else "⚠️  CHECK"
            
            print(f"{scenario_name:<20} {actual_risk:<12} {expected_risk:<10} {violations:<12} {status:<10}")
        
        print("\nAPI Call Summary:")
        print(f"Jira API calls: {len(self.mock_jira.get_call_log())}")
        print(f"Tufin API calls: {len(self.mock_tufin.get_call_log())}")
    
    def run_all_tests(self) -> bool:
        """Run all debug tests."""
        print("🚀 Starting Firewall Automation Debug Tests")
        
        tests = [
            ("Environment Setup", self.test_environment_setup),
            ("Mock Clients", self.test_mock_clients),
            ("Rule Parsing", self.test_parsing),
            ("Security Validation", self.test_security_validation),
            ("Tufin Analysis", self.test_tufin_analysis),
            ("End-to-End Workflow", self.test_end_to_end_workflow)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} - PASSED")
                else:
                    print(f"❌ {test_name} - FAILED")
            except Exception as e:
                print(f"💥 {test_name} - ERROR: {e}")
        
        self.print_summary()
        
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! System is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            return False


def main():
    """Main entry point for debug testing."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Debug test runner for firewall automation')
    parser.add_argument('--quiet', '-q', action='store_true', help='Reduce output verbosity')
    parser.add_argument('--test', '-t', choices=['env', 'clients', 'parsing', 'validation', 'tufin', 'e2e'], 
                       help='Run specific test only')
    
    args = parser.parse_args()
    
    runner = DebugTestRunner(verbose=not args.quiet)
    
    if args.test:
        test_map = {
            'env': runner.test_environment_setup,
            'clients': runner.test_mock_clients,
            'parsing': runner.test_parsing,
            'validation': runner.test_security_validation,
            'tufin': runner.test_tufin_analysis,
            'e2e': runner.test_end_to_end_workflow
        }
        
        if args.test in test_map:
            success = test_map[args.test]()
            sys.exit(0 if success else 1)
    else:
        success = runner.run_all_tests()
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
