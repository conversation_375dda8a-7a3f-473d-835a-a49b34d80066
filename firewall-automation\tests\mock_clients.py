"""
Mock clients for testing without external API dependencies.
"""

from typing import Optional, Dict, Any, List
import json
import time
from .mock_data import MockJiraData, MockTufinData


class MockJiraClient:
    """Mock Jira client that simulates API responses."""
    
    def __init__(self):
        self.connected = False
        self.call_log = []
        
        # Pre-defined mock issues
        self.mock_issues = {
            'FW-123': MockJiraData.get_sample_issue_good(),
            'FW-456': MockJiraData.get_sample_issue_risky(),
            'FW-789': MockJiraData.get_sample_issue_suspicious(),
            'FW-999': MockJiraData.get_sample_issue_critical(),
            'FW-666': MockJiraData.get_sample_issue_attack_pattern(),
        }
    
    def connect(self) -> bool:
        """Simulate connection to Jira."""
        self.call_log.append(('connect', time.time()))
        # Simulate connection delay
        time.sleep(0.1)
        self.connected = True
        return True
    
    def get_issue(self, issue_key: str) -> Optional[Dict[str, Any]]:
        """Simulate retrieving a Jira issue."""
        self.call_log.append(('get_issue', issue_key, time.time()))
        
        if not self.connected:
            return None
        
        # Simulate API delay
        time.sleep(0.2)
        
        # Return mock data if available
        if issue_key in self.mock_issues:
            return self.mock_issues[issue_key]
        
        # Return a generic issue for unknown keys
        return {
            'key': issue_key,
            'summary': f'Mock issue {issue_key}',
            'description': 'No specific mock data available',
            'status': 'Open',
            'priority': 'Medium',
            'assignee': 'Mock User',
            'reporter': 'Mock Reporter',
            'created': '2024-01-10T10:00:00.000Z',
            'updated': '2024-01-10T10:00:00.000Z',
            'comments': []
        }
    
    def add_comment(self, issue_key: str, comment_body: str) -> bool:
        """Simulate adding a comment to Jira issue."""
        self.call_log.append(('add_comment', issue_key, len(comment_body), time.time()))
        
        if not self.connected:
            return False
        
        # Simulate API delay
        time.sleep(0.3)
        
        # Add comment to mock issue if it exists
        if issue_key in self.mock_issues:
            self.mock_issues[issue_key]['comments'].append({
                'author': 'Firewall Automation System',
                'body': comment_body,
                'created': time.strftime('%Y-%m-%dT%H:%M:%S.000Z')
            })
        
        return True
    
    def update_issue_field(self, issue_key: str, field_name: str, field_value: Any) -> bool:
        """Simulate updating a Jira issue field."""
        self.call_log.append(('update_field', issue_key, field_name, time.time()))
        
        if not self.connected:
            return False
        
        # Simulate API delay
        time.sleep(0.2)
        return True
    
    def search_issues(self, jql: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Simulate searching Jira issues."""
        self.call_log.append(('search_issues', jql, max_results, time.time()))
        
        if not self.connected:
            return []
        
        # Simulate API delay
        time.sleep(0.4)
        
        # Return subset of mock issues
        return [
            {
                'key': issue['key'],
                'summary': issue['summary'],
                'status': issue['status'],
                'created': issue['created']
            }
            for issue in list(self.mock_issues.values())[:max_results]
        ]
    
    def get_call_log(self) -> List[tuple]:
        """Get log of all API calls made."""
        return self.call_log.copy()


class MockTufinClient:
    """Mock Tufin client that simulates API responses."""
    
    def __init__(self):
        self.authenticated = False
        self.call_log = []
        self.mock_rules = MockTufinData.get_existing_rules()
        self.mock_devices = MockTufinData.get_devices()
    
    def authenticate(self) -> bool:
        """Simulate authentication with Tufin."""
        self.call_log.append(('authenticate', time.time()))
        # Simulate auth delay
        time.sleep(0.1)
        self.authenticated = True
        return True
    
    def get_devices(self) -> List[Dict[str, Any]]:
        """Simulate getting firewall devices."""
        self.call_log.append(('get_devices', time.time()))
        
        if not self.authenticated:
            return []
        
        # Simulate API delay
        time.sleep(0.2)
        return self.mock_devices.copy()
    
    def search_rules(
        self,
        source: Optional[str] = None,
        destination: Optional[str] = None,
        port: Optional[str] = None,
        protocol: Optional[str] = None,
        device_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Simulate searching for firewall rules."""
        search_params = {
            'source': source,
            'destination': destination,
            'port': port,
            'protocol': protocol,
            'device_id': device_id
        }
        self.call_log.append(('search_rules', search_params, time.time()))
        
        if not self.authenticated:
            return []
        
        # Simulate API delay
        time.sleep(0.3)
        
        # Simple mock filtering
        results = []
        for rule in self.mock_rules:
            match = True
            
            if source and rule.get('source') != source:
                # Simple contains check for demo
                if source not in rule.get('source', ''):
                    match = False
            
            if destination and rule.get('destination') != destination:
                if destination not in rule.get('destination', ''):
                    match = False
            
            if port and rule.get('port') != port:
                match = False
            
            if protocol and rule.get('protocol', '').lower() != protocol.lower():
                match = False
            
            if match:
                results.append(rule.copy())
        
        return results
    
    def get_rule_details(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """Simulate getting detailed rule information."""
        self.call_log.append(('get_rule_details', rule_id, time.time()))
        
        if not self.authenticated:
            return None
        
        # Simulate API delay
        time.sleep(0.2)
        
        # Find rule by ID
        for rule in self.mock_rules:
            if rule.get('id') == rule_id:
                return rule.copy()
        
        return None
    
    def analyze_rule_impact(
        self,
        source: str,
        destination: str,
        port: str,
        protocol: str
    ) -> Dict[str, Any]:
        """Simulate rule impact analysis."""
        analysis_params = {
            'source': source,
            'destination': destination,
            'port': port,
            'protocol': protocol
        }
        self.call_log.append(('analyze_rule_impact', analysis_params, time.time()))
        
        if not self.authenticated:
            return {}
        
        # Simulate analysis delay
        time.sleep(0.5)
        
        # Find existing and overlapping rules
        existing_rules = self.search_rules(source, destination, port, protocol)
        overlapping_rules = self.search_rules(destination=destination, port=port)
        
        # Generate mock analysis
        conflicts = []
        recommendations = []
        
        if existing_rules:
            conflicts.append({
                'type': 'duplicate_rule',
                'message': f"Found {len(existing_rules)} existing rules with similar criteria",
                'severity': 'medium'
            })
            recommendations.append("Review existing rules before creating new ones")
        
        if len(overlapping_rules) > 3:
            conflicts.append({
                'type': 'too_many_overlapping_rules',
                'message': f"Found {len(overlapping_rules)} overlapping rules",
                'severity': 'low'
            })
            recommendations.append("Consider consolidating overlapping rules")
        
        # Check for suspicious patterns
        if port in ['31337', '4444', '12345']:
            conflicts.append({
                'type': 'suspicious_port',
                'message': f"Port {port} is commonly associated with malicious activity",
                'severity': 'high'
            })
            recommendations.append("Verify legitimate business need for this port")
        
        return {
            'existing_rules': existing_rules,
            'overlapping_rules': overlapping_rules,
            'conflicts': conflicts,
            'recommendations': recommendations
        }
    
    def get_policy_violations(self, rule_criteria: Dict[str, str]) -> List[Dict[str, Any]]:
        """Simulate policy violation checks."""
        self.call_log.append(('get_policy_violations', rule_criteria, time.time()))
        
        if not self.authenticated:
            return []
        
        # Simulate analysis delay
        time.sleep(0.3)
        
        violations = []
        
        # Mock policy checks
        if rule_criteria.get('source') in ['any', '*', '0.0.0.0/0']:
            violations.append({
                'type': 'overly_permissive_source',
                'severity': 'high',
                'message': 'Source address is too permissive',
                'recommendation': 'Specify exact source networks'
            })
        
        if rule_criteria.get('destination') in ['any', '*', '0.0.0.0/0']:
            violations.append({
                'type': 'overly_permissive_destination',
                'severity': 'high',
                'message': 'Destination address is too permissive',
                'recommendation': 'Specify exact destination networks'
            })
        
        if rule_criteria.get('protocol', '').lower() in ['telnet', 'ftp', 'http']:
            violations.append({
                'type': 'insecure_protocol',
                'severity': 'medium',
                'message': f"Protocol {rule_criteria.get('protocol')} is not secure",
                'recommendation': 'Use encrypted alternatives'
            })
        
        return violations
    
    def get_call_log(self) -> List[tuple]:
        """Get log of all API calls made."""
        return self.call_log.copy()
