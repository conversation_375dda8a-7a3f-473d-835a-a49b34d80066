#!/usr/bin/env python3
"""
Interactive debug tool for exploring firewall automation scenarios.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "tests"))

from src.jira.parser import FirewallTableParser, FirewallRule
from src.security.validator import SecurityValidator
from src.tufin.analyzer import TufinAnalyzer
from tests.mock_clients import MockTufinClient
from tests.mock_data import TEST_SCENARIOS


class InteractiveDebugger:
    """Interactive debugger for firewall automation."""
    
    def __init__(self):
        self.parser = FirewallTableParser()
        self.validator = SecurityValidator()
        self.analyzer = TufinAnalyzer(MockTufinClient())
        
    def show_menu(self):
        """Display the main menu."""
        print("\n" + "="*60)
        print(" 🔥 Firewall Automation Interactive Debugger")
        print("="*60)
        print("1. Test predefined scenarios")
        print("2. Create custom firewall rule")
        print("3. Analyze Jira issue format")
        print("4. Test security standards")
        print("5. Explore risk scoring")
        print("6. View security standards reference")
        print("0. Exit")
        print("-"*60)
    
    def test_scenarios(self):
        """Test predefined scenarios."""
        print("\n📋 Available Test Scenarios:")
        print("-"*40)
        
        scenarios = list(TEST_SCENARIOS.keys())
        for i, scenario in enumerate(scenarios, 1):
            info = TEST_SCENARIOS[scenario]
            print(f"{i}. {info['name']}")
            print(f"   Description: {info['description']}")
            print(f"   Expected Risk: {info['expected_risk_score']}")
            print()
        
        try:
            choice = input("Select scenario (1-{}) or 'all': ".format(len(scenarios)))
            
            if choice.lower() == 'all':
                for scenario_key in scenarios:
                    self._analyze_scenario(scenario_key)
            else:
                idx = int(choice) - 1
                if 0 <= idx < len(scenarios):
                    self._analyze_scenario(scenarios[idx])
                else:
                    print("❌ Invalid selection")
        except (ValueError, KeyboardInterrupt):
            print("❌ Invalid input")
    
    def _analyze_scenario(self, scenario_key: str):
        """Analyze a specific scenario."""
        scenario = TEST_SCENARIOS[scenario_key]
        print(f"\n🔍 Analyzing: {scenario['name']}")
        print("="*50)
        
        # Parse the issue
        request = self.parser.parse_issue(scenario['jira_data'])
        
        print(f"Issue Key: {request.issue_key}")
        print(f"Summary: {scenario['jira_data']['summary']}")
        print(f"Rules Found: {len(request.rules)}")
        
        if request.parsing_errors:
            print(f"⚠️  Parsing Errors: {request.parsing_errors}")
        
        # Analyze each rule
        for i, rule in enumerate(request.rules, 1):
            print(f"\n--- Rule {i} ---")
            self._analyze_rule(rule)
        
        input("\nPress Enter to continue...")
    
    def _analyze_rule(self, rule: FirewallRule):
        """Analyze a single firewall rule."""
        print(f"Source: {rule.source}")
        print(f"Destination: {rule.destination}")
        print(f"Port: {rule.port}")
        print(f"Protocol: {rule.protocol}")
        print(f"Action: {rule.action}")
        print(f"Justification: {rule.justification}")
        
        # Security validation
        print("\n🔒 Security Validation:")
        result = self.validator.validate_rule(rule)
        
        print(f"Compliant: {'✅ Yes' if result.is_compliant else '❌ No'}")
        print(f"Risk Score: {result.total_risk_score}")
        
        if result.violations:
            print("Violations:")
            for violation in result.violations:
                severity_icon = {
                    'critical': '🚨',
                    'high': '⚠️',
                    'medium': '⚡',
                    'low': 'ℹ️'
                }.get(violation.severity.value, 'ℹ️')
                
                print(f"  {severity_icon} {violation.type.value}: {violation.message}")
                print(f"     Score: {violation.risk_score}, Recommendation: {violation.recommendation}")
        
        # Tufin analysis
        print("\n🔧 Tufin Analysis:")
        analysis = self.analyzer.analyze_rule(rule)
        
        print(f"Existing Rules: {len(analysis.existing_rules)}")
        print(f"Conflicts: {len(analysis.conflicts)}")
        print(f"Should Create New: {analysis.should_create_new}")
        
        if analysis.conflicts:
            print("Conflicts:")
            for conflict in analysis.conflicts:
                print(f"  - {conflict.type.value}: {conflict.message}")
        
        if analysis.recommendations:
            print("Recommendations:")
            for rec in analysis.recommendations:
                print(f"  - {rec}")
    
    def create_custom_rule(self):
        """Create and analyze a custom firewall rule."""
        print("\n🛠️  Create Custom Firewall Rule")
        print("-"*40)
        
        try:
            source = input("Source address: ").strip()
            destination = input("Destination address: ").strip()
            port = input("Port: ").strip()
            protocol = input("Protocol: ").strip()
            action = input("Action (allow/deny): ").strip() or "allow"
            justification = input("Business justification: ").strip()
            
            rule = FirewallRule(
                source=source,
                destination=destination,
                port=port,
                protocol=protocol,
                action=action,
                justification=justification
            )
            
            print(f"\n🔍 Analyzing Custom Rule:")
            self._analyze_rule(rule)
            
        except KeyboardInterrupt:
            print("\n❌ Cancelled")
    
    def analyze_jira_format(self):
        """Test different Jira table formats."""
        print("\n📝 Jira Issue Format Analyzer")
        print("-"*40)
        
        formats = {
            "1": ("Jira Table Markup", '''
||Field||Value||
|Source|*************|
|Destination|*********|
|Port|443|
|Protocol|HTTPS|
            '''),
            "2": ("Markdown Table", '''
| Field | Value |
|-------|-------|
| Source | ************* |
| Destination | ********* |
| Port | 443 |
| Protocol | HTTPS |
            '''),
            "3": ("Field-Value Pairs", '''
Source: *************
Destination: *********
Port: 443
Protocol: HTTPS
Action: Allow
Business Justification: Web server access for customer portal
            ''')
        }
        
        print("Available formats:")
        for key, (name, _) in formats.items():
            print(f"{key}. {name}")
        
        choice = input("\nSelect format to test (1-3): ").strip()
        
        if choice in formats:
            name, description = formats[choice]
            print(f"\n📋 Testing: {name}")
            print("Sample format:")
            print(description)
            
            # Create mock issue
            mock_issue = {
                'key': 'TEST-001',
                'summary': 'Test parsing',
                'description': description
            }
            
            # Parse it
            request = self.parser.parse_issue(mock_issue)
            
            print(f"\n✅ Parsing Results:")
            print(f"Rules found: {len(request.rules)}")
            print(f"Errors: {len(request.parsing_errors)}")
            
            if request.parsing_errors:
                print("Errors:")
                for error in request.parsing_errors:
                    print(f"  - {error}")
            
            for i, rule in enumerate(request.rules, 1):
                print(f"\nRule {i}:")
                print(f"  Source: {rule.source}")
                print(f"  Destination: {rule.destination}")
                print(f"  Port: {rule.port}")
                print(f"  Protocol: {rule.protocol}")
        else:
            print("❌ Invalid selection")
    
    def test_security_standards(self):
        """Test security standards against various inputs."""
        print("\n🔒 Security Standards Tester")
        print("-"*40)
        
        test_cases = [
            ("Wildcard source", "*", "10.0.0.100", "443", "HTTPS"),
            ("Critical protocol", "*************", "10.0.0.100", "23", "telnet"),
            ("Suspicious port", "*************", "10.0.0.100", "31337", "TCP"),
            ("Database port", "*************", "10.0.0.100", "1433", "TCP"),
            ("P2P port", "*************", "10.0.0.100", "6881", "TCP"),
            ("Safe rule", "*************", "10.0.0.100", "443", "HTTPS")
        ]
        
        for name, source, dest, port, protocol in test_cases:
            print(f"\n--- {name} ---")
            rule = FirewallRule(
                source=source,
                destination=dest,
                port=port,
                protocol=protocol,
                justification="Test rule for security standards validation"
            )
            
            result = self.validator.validate_rule(rule)
            print(f"Risk Score: {result.total_risk_score}")
            print(f"Violations: {len(result.violations)}")
            
            if result.violations:
                for violation in result.violations:
                    print(f"  - {violation.type.value}: {violation.message}")
    
    def explore_risk_scoring(self):
        """Explore risk scoring system."""
        print("\n📊 Risk Scoring Explorer")
        print("-"*40)
        
        if not self.validator.standards:
            print("❌ Security standards not loaded")
            return
        
        risk_scores = self.validator.standards.get('risk_scoring', {})
        
        print("Risk Score Categories:")
        for category, score in sorted(risk_scores.items()):
            if isinstance(score, (int, float)):
                print(f"  {category}: {score}")
        
        print(f"\nTotal categories: {len([s for s in risk_scores.values() if isinstance(s, (int, float))])}")
        
        # Show risk scale
        print("\nRisk Scale:")
        print("  1-2: Minimal Risk")
        print("  3-5: Low Risk") 
        print("  6-8: Medium Risk")
        print("  9-15: High Risk")
        print("  16+: Critical Risk")
    
    def view_security_reference(self):
        """View security standards reference."""
        print("\n📖 Security Standards Reference")
        print("-"*40)
        
        if not self.validator.standards:
            print("❌ Security standards not loaded")
            return
        
        standards = self.validator.standards
        
        print(f"Version: {standards.get('version', 'unknown')}")
        print(f"Last Updated: {standards.get('last_updated', 'unknown')}")
        
        sections = [
            ('prohibited_sources', 'Prohibited Sources'),
            ('prohibited_destinations', 'Prohibited Destinations'),
            ('risky_protocols', 'Risky Protocols'),
            ('dangerous_ports', 'Dangerous Ports'),
            ('suspicious_patterns', 'Suspicious Patterns')
        ]
        
        for key, title in sections:
            if key in standards:
                print(f"\n{title}:")
                section = standards[key]
                if isinstance(section, dict):
                    for subkey, values in section.items():
                        if isinstance(values, list) and subkey != 'description':
                            print(f"  {subkey}: {', '.join(values[:5])}{'...' if len(values) > 5 else ''}")
    
    def run(self):
        """Run the interactive debugger."""
        while True:
            try:
                self.show_menu()
                choice = input("Select option (0-6): ").strip()
                
                if choice == '0':
                    print("👋 Goodbye!")
                    break
                elif choice == '1':
                    self.test_scenarios()
                elif choice == '2':
                    self.create_custom_rule()
                elif choice == '3':
                    self.analyze_jira_format()
                elif choice == '4':
                    self.test_security_standards()
                elif choice == '5':
                    self.explore_risk_scoring()
                elif choice == '6':
                    self.view_security_reference()
                else:
                    print("❌ Invalid option")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"💥 Error: {e}")


def main():
    """Main entry point."""
    debugger = InteractiveDebugger()
    debugger.run()


if __name__ == '__main__':
    main()
