"""
Tufin rule analysis and recommendation engine.
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..jira.parser import FirewallRule
from ..utils.logger import get_logger
from .client import TufinClient


class ConflictType(Enum):
    """Types of rule conflicts."""
    DUPLICATE_RULE = "duplicate_rule"
    OVERLAPPING_RULE = "overlapping_rule"
    CONFLICTING_ACTION = "conflicting_action"
    SHADOWED_RULE = "shadowed_rule"
    TOO_PERMISSIVE = "too_permissive"


@dataclass
class RuleConflict:
    """Represents a rule conflict or issue."""
    type: ConflictType
    severity: str
    message: str
    existing_rule_id: Optional[str] = None
    recommendation: Optional[str] = None


@dataclass
class RuleAnalysis:
    """Analysis result for a firewall rule."""
    rule: FirewallRule
    existing_rules: List[Dict[str, Any]]
    conflicts: List[RuleConflict]
    recommendations: List[str]
    should_create_new: bool
    should_modify_existing: bool


class TufinAnalyzer:
    """Analyzer for firewall rules using Tufin data."""

    def __init__(self, tufin_client: Optional[TufinClient] = None):
        self.logger = get_logger(__name__)
        self.tufin_client = tufin_client or TufinClient()

    def analyze_rule(self, rule: FirewallRule) -> RuleAnalysis:
        """
        Analyze a firewall rule for conflicts and recommendations.

        Args:
            rule: FirewallRule to analyze

        Returns:
            RuleAnalysis with findings and recommendations
        """
        self.logger.info(
            "Analyzing firewall rule",
            source=rule.source,
            destination=rule.destination,
            port=rule.port,
            protocol=rule.protocol
        )

        # Search for existing rules
        existing_rules = self._find_existing_rules(rule)

        # Analyze conflicts
        conflicts = self._analyze_conflicts(rule, existing_rules)

        # Generate recommendations
        recommendations = self._generate_recommendations(rule, existing_rules, conflicts)

        # Determine action recommendations
        should_create_new, should_modify_existing = self._determine_actions(
            existing_rules, conflicts
        )

        analysis = RuleAnalysis(
            rule=rule,
            existing_rules=existing_rules,
            conflicts=conflicts,
            recommendations=recommendations,
            should_create_new=should_create_new,
            should_modify_existing=should_modify_existing
        )

        self.logger.info(
            "Completed rule analysis",
            existing_rules_count=len(existing_rules),
            conflicts_count=len(conflicts),
            should_create_new=should_create_new,
            should_modify_existing=should_modify_existing
        )

        return analysis

    def _find_existing_rules(self, rule: FirewallRule) -> List[Dict[str, Any]]:
        """Find existing rules that might be related."""
        existing_rules = []

        try:
            # Search for exact matches
            exact_matches = self.tufin_client.search_rules(
                source=rule.source,
                destination=rule.destination,
                port=rule.port,
                protocol=rule.protocol
            )
            existing_rules.extend(exact_matches)

            # Search for overlapping rules (same destination/port)
            overlapping = self.tufin_client.search_rules(
                destination=rule.destination,
                port=rule.port
            )

            # Filter out exact matches to avoid duplicates
            for overlap_rule in overlapping:
                if overlap_rule not in exact_matches:
                    existing_rules.append(overlap_rule)

            # Limit results to avoid overwhelming analysis
            existing_rules = existing_rules[:20]

        except Exception as e:
            self.logger.error(
                "Error searching for existing rules",
                error=str(e)
            )

        return existing_rules

    def _analyze_conflicts(
        self,
        rule: FirewallRule,
        existing_rules: List[Dict[str, Any]]
    ) -> List[RuleConflict]:
        """Analyze potential conflicts with existing rules."""
        conflicts = []

        for existing_rule in existing_rules:
            # Check for duplicate rules
            if self._is_duplicate_rule(rule, existing_rule):
                conflicts.append(RuleConflict(
                    type=ConflictType.DUPLICATE_RULE,
                    severity="high",
                    message=f"Duplicate rule found: {existing_rule.get('id', 'unknown')}",
                    existing_rule_id=existing_rule.get('id'),
                    recommendation="Consider modifying existing rule instead of creating new one"
                ))

            # Check for overlapping rules
            elif self._is_overlapping_rule(rule, existing_rule):
                conflicts.append(RuleConflict(
                    type=ConflictType.OVERLAPPING_RULE,
                    severity="medium",
                    message=f"Overlapping rule found: {existing_rule.get('id', 'unknown')}",
                    existing_rule_id=existing_rule.get('id'),
                    recommendation="Review rule order and consolidation opportunities"
                ))

            # Check for conflicting actions
            if self._has_conflicting_action(rule, existing_rule):
                conflicts.append(RuleConflict(
                    type=ConflictType.CONFLICTING_ACTION,
                    severity="high",
                    message=f"Conflicting action with rule: {existing_rule.get('id', 'unknown')}",
                    existing_rule_id=existing_rule.get('id'),
                    recommendation="Resolve action conflicts before implementation"
                ))

        # Check for overly permissive rules
        if self._is_too_permissive(rule):
            conflicts.append(RuleConflict(
                type=ConflictType.TOO_PERMISSIVE,
                severity="medium",
                message="Rule is overly permissive",
                recommendation="Consider more restrictive source/destination specifications"
            ))

        return conflicts

    def _generate_recommendations(
        self,
        rule: FirewallRule,
        existing_rules: List[Dict[str, Any]],
        conflicts: List[RuleConflict]
    ) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []

        # Recommendations based on conflicts
        for conflict in conflicts:
            if conflict.recommendation:
                recommendations.append(conflict.recommendation)

        # General recommendations
        if len(existing_rules) > 10:
            recommendations.append(
                "Consider consolidating multiple rules for the same destination"
            )

        if not existing_rules:
            recommendations.append(
                "No existing rules found - new rule creation recommended"
            )

        # Rule optimization recommendations
        if self._can_consolidate_rules(rule, existing_rules):
            recommendations.append(
                "Opportunity to consolidate with existing rules identified"
            )

        if self._should_use_groups(rule, existing_rules):
            recommendations.append(
                "Consider using network/service groups for better management"
            )

        return list(set(recommendations))  # Remove duplicates

    def _determine_actions(
        self,
        existing_rules: List[Dict[str, Any]],
        conflicts: List[RuleConflict]
    ) -> tuple[bool, bool]:
        """Determine recommended actions."""
        should_create_new = True
        should_modify_existing = False

        # Check for duplicate rules
        duplicate_conflicts = [
            c for c in conflicts
            if c.type == ConflictType.DUPLICATE_RULE
        ]

        if duplicate_conflicts:
            should_create_new = False
            should_modify_existing = True

        # Check for high-severity conflicts
        high_severity_conflicts = [
            c for c in conflicts
            if c.severity == "high"
        ]

        if high_severity_conflicts and not duplicate_conflicts:
            should_create_new = False  # Don't create until conflicts resolved

        return should_create_new, should_modify_existing

    def _is_duplicate_rule(self, rule: FirewallRule, existing_rule: Dict[str, Any]) -> bool:
        """Check if rule is a duplicate of existing rule."""
        # Simplified comparison - in real implementation, this would be more sophisticated
        return (
            existing_rule.get('source') == rule.source and
            existing_rule.get('destination') == rule.destination and
            existing_rule.get('port') == rule.port and
            existing_rule.get('protocol', '').lower() == rule.protocol.lower()
        )

    def _is_overlapping_rule(self, rule: FirewallRule, existing_rule: Dict[str, Any]) -> bool:
        """Check if rule overlaps with existing rule."""
        # Check for same destination and port but different source
        return (
            existing_rule.get('destination') == rule.destination and
            existing_rule.get('port') == rule.port and
            existing_rule.get('source') != rule.source
        )

    def _has_conflicting_action(self, rule: FirewallRule, existing_rule: Dict[str, Any]) -> bool:
        """Check if rule has conflicting action with existing rule."""
        # Check for same source/destination but different actions
        if (existing_rule.get('source') == rule.source and
            existing_rule.get('destination') == rule.destination):

            existing_action = existing_rule.get('action', 'allow').lower()
            rule_action = rule.action.lower()

            return existing_action != rule_action

        return False

    def _is_too_permissive(self, rule: FirewallRule) -> bool:
        """Check if rule is overly permissive."""
        permissive_sources = ['*', 'any', '0.0.0.0/0', '::/0']
        permissive_destinations = ['*', 'any', '0.0.0.0/0', '::/0']

        return (
            rule.source.lower() in [p.lower() for p in permissive_sources] or
            rule.destination.lower() in [p.lower() for p in permissive_destinations]
        )

    def _can_consolidate_rules(
        self,
        rule: FirewallRule,
        existing_rules: List[Dict[str, Any]]
    ) -> bool:
        """Check if rule can be consolidated with existing rules."""
        # Look for rules with same destination/port but different sources
        similar_rules = [
            r for r in existing_rules
            if (r.get('destination') == rule.destination and
                r.get('port') == rule.port and
                r.get('protocol', '').lower() == rule.protocol.lower())
        ]

        return len(similar_rules) >= 2

    def _should_use_groups(
        self,
        rule: FirewallRule,
        existing_rules: List[Dict[str, Any]]
    ) -> bool:
        """Check if network/service groups should be used."""
        # Recommend groups if there are many rules for the same destination
        same_destination_rules = [
            r for r in existing_rules
            if r.get('destination') == rule.destination
        ]

        return len(same_destination_rules) >= 5
