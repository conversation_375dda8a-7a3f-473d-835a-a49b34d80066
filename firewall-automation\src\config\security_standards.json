{"version": "1.0", "last_updated": "2024-01-01", "description": "Security standards for firewall change validation", "prohibited_sources": {"wildcards": ["*", "any", "0.0.0.0/0", "::/0"], "description": "Source addresses that are too permissive"}, "prohibited_destinations": {"wildcards": ["*", "any", "0.0.0.0/0", "::/0"], "description": "Destination addresses that are too permissive"}, "risky_protocols": {"high_risk": ["ssh", "telnet", "ftp", "tftp", "snmp", "rsh", "rlogin"], "medium_risk": ["http", "pop3", "imap", "smtp"], "description": "Protocols that require additional scrutiny"}, "dangerous_ports": {"prohibited": ["23", "69", "135", "139", "445", "1433", "1521", "3389"], "requires_justification": ["22", "21", "80", "443", "25", "110", "143", "993", "995"], "description": "Ports that are either prohibited or require business justification"}, "port_ranges": {"max_range_size": 100, "prohibited_ranges": ["1-65535", "0-65535", "any"], "description": "Restrictions on port range specifications"}, "validation_rules": {"require_business_justification": true, "require_expiration_date": false, "require_approval": true, "max_rule_duration_days": 365, "description": "General validation requirements"}, "risk_scoring": {"wildcard_source": 10, "wildcard_destination": 8, "risky_protocol_high": 9, "risky_protocol_medium": 5, "dangerous_port": 7, "large_port_range": 6, "missing_justification": 4, "description": "Risk scores for different violation types (1-10 scale)"}, "required_fields": ["source", "destination", "port", "protocol", "action", "business_justification"], "justification_keywords": {"acceptable": ["business", "approved", "temporary", "migration", "maintenance", "backup"], "requires_review": ["emergency", "urgent", "asap"], "description": "Keywords that indicate proper or concerning justifications"}}