{"version": "2.0", "last_updated": "2024-12-19", "description": "Comprehensive security standards for firewall change validation based on industry best practices", "prohibited_sources": {"wildcards": ["*", "any", "0.0.0.0/0", "::/0", "all", "internet"], "untrusted_networks": ["0.0.0.0/0", "::/0", "internet", "dmz", "guest"], "description": "Source addresses that are too permissive or from untrusted networks"}, "prohibited_destinations": {"wildcards": ["*", "any", "0.0.0.0/0", "::/0", "all", "internet"], "critical_internal": ["domain_controller", "dc", "ad", "ldap_server", "dns_server"], "description": "Destination addresses that are too permissive or critical infrastructure"}, "risky_protocols": {"critical_risk": ["telnet", "rsh", "rlogin", "rexec", "tftp", "finger", "chargen", "echo"], "high_risk": ["ftp", "snmp", "snmpv1", "snmpv2", "netbios", "smb", "cifs", "nfs", "x11"], "medium_risk": ["ssh", "http", "pop3", "imap", "smtp", "ldap", "kerber<PERSON>", "radius"], "legacy_protocols": ["appletalk", "ipx", "netbeui", "decnet"], "description": "Protocols categorized by security risk level"}, "dangerous_ports": {"prohibited": ["23", "69", "79", "7", "9", "11", "13", "15", "17", "19", "20", "512", "513", "514", "515", "540"], "critical_infrastructure": ["53", "88", "135", "139", "389", "445", "464", "636", "3268", "3269"], "database_ports": ["1433", "1434", "1521", "1522", "3306", "5432", "1830", "50000", "60000"], "management_ports": ["22", "23", "80", "443", "161", "162", "623", "664", "8080", "8443", "9090"], "remote_access": ["3389", "5900", "5901", "5902", "22", "23", "992", "5985", "5986"], "requires_justification": ["21", "22", "25", "53", "80", "110", "143", "443", "993", "995", "1723", "1701"], "description": "Ports categorized by risk level and usage type"}, "suspicious_patterns": {"common_attack_ports": ["4444", "31337", "12345", "54321", "9999", "6666", "1234"], "p2p_ports": ["6881", "6882", "6883", "6884", "6885", "6886", "6887", "6888", "6889"], "tor_ports": ["9001", "9030", "9050", "9051", "9150", "9151"], "bitcoin_ports": ["8332", "8333", "18332", "18333"], "gaming_ports": ["27015", "7777", "7778", "28960"], "description": "Ports commonly associated with suspicious or non-business activities"}, "port_ranges": {"max_range_size": 50, "prohibited_ranges": ["1-65535", "0-65535", "any", "all", "1-1024", "1024-65535"], "suspicious_ranges": ["1-1000", "8000-9000", "31000-32000"], "description": "Restrictions on port range specifications"}, "network_security": {"rfc1918_external": {"networks": ["10.0.0.0/8", "**********/12", "***********/16"], "description": "Private networks that should not be routed externally"}, "multicast_broadcast": {"addresses": ["*********/4", "***************", "0.0.0.0"], "description": "Multicast and broadcast addresses"}, "loopback": {"addresses": ["*********/8", "::1/128"], "description": "Loopback addresses"}}, "compliance_frameworks": {"pci_dss": {"prohibited_protocols": ["telnet", "ftp", "http", "snmpv1", "snmpv2"], "required_encryption": ["ssh", "https", "sftp", "snmpv3"], "description": "PCI DSS compliance requirements"}, "hipaa": {"encryption_required": true, "audit_logging": true, "description": "HIPAA compliance requirements"}, "sox": {"segregation_required": true, "change_approval": true, "description": "SOX compliance requirements"}}, "validation_rules": {"require_business_justification": true, "require_expiration_date": false, "require_approval": true, "max_rule_duration_days": 365, "min_justification_length": 20, "require_change_window": true, "description": "General validation requirements"}, "risk_scoring": {"wildcard_source": 10, "wildcard_destination": 9, "critical_protocol": 10, "risky_protocol_high": 8, "risky_protocol_medium": 5, "legacy_protocol": 7, "prohibited_port": 10, "critical_infrastructure_port": 9, "database_port": 8, "management_port": 7, "remote_access_port": 6, "suspicious_port": 8, "p2p_port": 7, "large_port_range": 6, "suspicious_range": 5, "missing_justification": 4, "weak_justification": 3, "rfc1918_external": 8, "multicast_broadcast": 6, "description": "Risk scores for different violation types (1-10 scale)"}, "required_fields": ["source", "destination", "port", "protocol", "action", "business_justification", "requestor", "change_window"], "justification_keywords": {"acceptable": ["business_requirement", "approved_by", "temporary_access", "migration", "maintenance_window", "backup_solution", "monitoring", "integration", "vendor_access", "application_requirement", "database_connection", "api_access", "service_communication", "load_balancer", "clustering"], "requires_review": ["emergency", "urgent", "asap", "immediate", "critical", "outage", "production_down", "customer_impact", "revenue_impact"], "red_flags": ["testing", "temporary", "quick_fix", "workaround", "bypass", "troubleshooting", "debug", "personal", "convenience", "easy_access"], "compliance_terms": ["pci_compliant", "hipaa_approved", "sox_requirement", "audit_approved", "security_reviewed", "risk_assessed", "change_approved"], "description": "Keywords that indicate different levels of justification quality"}, "time_based_rules": {"high_risk_hours": {"start": "18:00", "end": "06:00", "description": "Hours when changes carry higher risk"}, "maintenance_windows": {"preferred_days": ["saturday", "sunday"], "preferred_hours": ["02:00", "04:00"], "description": "Preferred maintenance windows"}, "emergency_approval": {"max_duration_hours": 24, "requires_followup": true, "description": "Emergency change rules"}}, "network_zones": {"dmz": {"risk_multiplier": 1.5, "description": "DMZ zone requires additional scrutiny"}, "internal": {"risk_multiplier": 1.0, "description": "Internal network baseline risk"}, "external": {"risk_multiplier": 2.0, "description": "External access carries highest risk"}, "management": {"risk_multiplier": 1.8, "description": "Management network access"}}, "action_types": {"allow": {"risk_multiplier": 1.0, "description": "Standard allow rule"}, "deny": {"risk_multiplier": 0.5, "description": "Deny rules are generally safer"}, "log": {"risk_multiplier": 0.3, "description": "Log-only rules for monitoring"}, "nat": {"risk_multiplier": 1.3, "description": "NAT rules require additional review"}}}