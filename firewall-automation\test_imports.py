#!/usr/bin/env python3
"""
Simple test to verify all imports are working correctly.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "tests"))

def test_imports():
    """Test that all modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        # Test config imports
        print("  Testing config...")
        from src.config.settings import settings
        print("    ✅ settings imported")
        
        # Test utils imports
        print("  Testing utils...")
        from src.utils.logger import setup_logging, get_logger
        from src.utils.helpers import validate_ip_address, parse_port_range
        print("    ✅ utils imported")
        
        # Test jira imports
        print("  Testing jira...")
        from src.jira.client import JiraClient
        from src.jira.parser import FirewallTableParser, FirewallRule
        print("    ✅ jira imported")
        
        # Test security imports
        print("  Testing security...")
        from src.security.validator import SecurityValidator, ViolationType, Severity
        print("    ✅ security imported")
        
        # Test tufin imports
        print("  Testing tufin...")
        from src.tufin.client import TufinClient
        from src.tufin.analyzer import TufinAnalyzer
        print("    ✅ tufin imported")
        
        # Test workflow imports
        print("  Testing workflow...")
        from src.workflow import FirewallAnalysisWorkflow
        print("    ✅ workflow imported")
        
        # Test mock imports
        print("  Testing mocks...")
        from tests.mock_clients import MockJiraClient, MockTufinClient
        from tests.mock_data import TEST_SCENARIOS
        print("    ✅ mocks imported")
        
        print("✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of key components."""
    print("\n🔧 Testing basic functionality...")
    
    try:
        # Test logger
        from src.utils.logger import get_logger
        logger = get_logger("test")
        logger.info("Test log message")
        print("    ✅ Logger working")
        
        # Test validator creation
        from src.security.validator import SecurityValidator
        validator = SecurityValidator()
        print("    ✅ Validator created")
        
        # Test parser creation
        from src.jira.parser import FirewallTableParser
        parser = FirewallTableParser()
        print("    ✅ Parser created")
        
        # Test mock clients
        from tests.mock_clients import MockJiraClient, MockTufinClient
        mock_jira = MockJiraClient()
        mock_tufin = MockTufinClient()
        print("    ✅ Mock clients created")
        
        print("✅ Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_validation():
    """Test a simple validation scenario."""
    print("\n🔒 Testing simple validation...")
    
    try:
        from src.security.validator import SecurityValidator
        from src.jira.parser import FirewallRule
        
        validator = SecurityValidator()
        
        # Create a simple rule
        rule = FirewallRule(
            source="192.168.1.100",
            destination="10.0.0.50",
            port="443",
            protocol="HTTPS",
            justification="Test rule for validation"
        )
        
        # Validate it
        result = validator.validate_rule(rule)
        
        print(f"    Rule validated: {result.is_compliant}")
        print(f"    Risk score: {result.total_risk_score}")
        print(f"    Violations: {len(result.violations)}")
        
        print("✅ Simple validation test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Firewall Automation Imports and Basic Functionality")
    print("=" * 70)
    
    tests = [
        ("Import Test", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Simple Validation", test_simple_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"💥 {test_name} - ERROR: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! You can now run the debug test runner.")
        print("\nNext steps:")
        print("  python debug_test_runner.py")
        print("  python interactive_debug.py")
        print("  python quick_test.py")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
