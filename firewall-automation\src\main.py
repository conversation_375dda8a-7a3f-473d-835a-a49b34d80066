#!/usr/bin/env python3
"""
Main entry point for the firewall change automation system.

This script can be called from Azure DevOps pipelines or run manually
to analyze Jira firewall change requests and provide security recommendations.
"""

import click
import sys
from pathlib import Path
from typing import Optional

from .config.settings import settings
from .utils.logger import setup_logging, get_logger, create_log_file_path


@click.command()
@click.option(
    '--jira-issue', 
    required=True, 
    help='Jira issue key (e.g., FW-123)'
)
@click.option(
    '--log-level', 
    default='INFO',
    type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']),
    help='Logging level'
)
@click.option(
    '--log-file', 
    help='Optional log file path'
)
@click.option(
    '--dry-run', 
    is_flag=True, 
    help='Run analysis without posting comments to Jira'
)
@click.option(
    '--output-dir', 
    default='./output',
    help='Directory for output files'
)
def main(
    jira_issue: str,
    log_level: str,
    log_file: Optional[str],
    dry_run: bool,
    output_dir: str
):
    """
    Analyze a Jira firewall change request and provide security recommendations.
    
    This tool will:
    1. Retrieve the specified Jira issue
    2. Parse firewall change table data
    3. Validate against security standards
    4. Query Tufin for existing rules
    5. Generate recommendations
    6. Post comments back to Jira (unless --dry-run)
    """
    # Set up logging
    if not log_file:
        log_file = create_log_file_path(f"{output_dir}/logs")
    
    logger = setup_logging(log_level=log_level, log_file=log_file)
    logger = get_logger(__name__)
    
    logger.info(
        "Starting firewall change automation",
        jira_issue=jira_issue,
        dry_run=dry_run,
        output_dir=output_dir
    )
    
    try:
        # Validate configuration
        if not settings.validate():
            logger.error("Configuration validation failed")
            sys.exit(1)
        
        # Create output directory
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # Import and run the main workflow
        from .workflow import FirewallAnalysisWorkflow
        
        workflow = FirewallAnalysisWorkflow(
            jira_issue=jira_issue,
            output_dir=output_dir,
            dry_run=dry_run,
            logger=logger
        )
        
        result = workflow.run()
        
        if result.success:
            logger.info(
                "Firewall analysis completed successfully",
                recommendations_count=len(result.recommendations),
                risk_score=result.risk_score
            )
            
            # Print summary to stdout for Azure DevOps
            print(f"✅ Analysis completed for {jira_issue}")
            print(f"📊 Risk Score: {result.risk_score}/10")
            print(f"📝 Recommendations: {len(result.recommendations)}")
            
            if result.recommendations:
                print("\n🔍 Key Findings:")
                for i, rec in enumerate(result.recommendations[:3], 1):
                    print(f"  {i}. {rec.title}")
            
            sys.exit(0)
        else:
            logger.error(
                "Firewall analysis failed",
                error=result.error_message
            )
            print(f"❌ Analysis failed: {result.error_message}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Analysis interrupted by user")
        print("\n⏹️  Analysis interrupted")
        sys.exit(130)
        
    except Exception as e:
        logger.exception("Unexpected error during analysis")
        print(f"💥 Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
