"""
Parser for extracting firewall change data from Jira issue descriptions.
"""

import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger


class FieldType(Enum):
    """Types of firewall change fields."""
    SOURCE = "source"
    DESTINATION = "destination"
    PORT = "port"
    PROTOCOL = "protocol"
    ACTION = "action"
    JUSTIFICATION = "justification"
    EXPIRATION = "expiration"
    PRIORITY = "priority"
    OTHER = "other"


@dataclass
class FirewallRule:
    """Represents a single firewall rule from the change request."""
    source: str
    destination: str
    port: str
    protocol: str
    action: str = "allow"
    justification: Optional[str] = None
    expiration: Optional[str] = None
    priority: Optional[str] = None
    raw_data: Optional[Dict[str, str]] = None


@dataclass
class FirewallChangeRequest:
    """Represents a complete firewall change request."""
    issue_key: str
    summary: str
    description: str
    rules: List[FirewallRule]
    raw_table_data: List[Dict[str, str]]
    parsing_errors: List[str]


class FirewallTableParser:
    """Parser for extracting firewall change data from Jira descriptions."""

    def __init__(self):
        self.logger = get_logger(__name__)

        # Field mapping patterns
        self.field_patterns = {
            FieldType.SOURCE: [
                r'source.*address',
                r'source.*ip',
                r'source.*host',
                r'from.*address',
                r'from.*ip',
                r'src'
            ],
            FieldType.DESTINATION: [
                r'destination.*address',
                r'destination.*ip',
                r'destination.*host',
                r'to.*address',
                r'to.*ip',
                r'dest',
                r'dst'
            ],
            FieldType.PORT: [
                r'port',
                r'service',
                r'application'
            ],
            FieldType.PROTOCOL: [
                r'protocol',
                r'proto'
            ],
            FieldType.ACTION: [
                r'action',
                r'rule.*type',
                r'permit',
                r'deny',
                r'allow',
                r'block'
            ],
            FieldType.JUSTIFICATION: [
                r'justification',
                r'business.*reason',
                r'purpose',
                r'reason',
                r'description'
            ],
            FieldType.EXPIRATION: [
                r'expir',
                r'end.*date',
                r'valid.*until',
                r'duration'
            ],
            FieldType.PRIORITY: [
                r'priority',
                r'urgency'
            ]
        }

    def parse_issue(self, issue_data: Dict[str, Any]) -> FirewallChangeRequest:
        """
        Parse a Jira issue to extract firewall change request data.

        Args:
            issue_data: Jira issue data dictionary

        Returns:
            FirewallChangeRequest object
        """
        self.logger.info(
            "Parsing firewall change request",
            issue_key=issue_data['key']
        )

        description = issue_data.get('description', '')

        # Extract table data from description
        table_data = self._extract_table_data(description)

        # Parse rules from table data
        rules, errors = self._parse_rules_from_table(table_data)

        request = FirewallChangeRequest(
            issue_key=issue_data['key'],
            summary=issue_data.get('summary', ''),
            description=description,
            rules=rules,
            raw_table_data=table_data,
            parsing_errors=errors
        )

        self.logger.info(
            "Parsed firewall change request",
            issue_key=issue_data['key'],
            rules_count=len(rules),
            errors_count=len(errors)
        )

        return request

    def _extract_table_data(self, description: str) -> List[Dict[str, str]]:
        """
        Extract table data from Jira description text.

        Args:
            description: Jira issue description

        Returns:
            List of dictionaries representing table rows
        """
        table_data = []

        # Try different table formats

        # Format 1: Jira table markup (||header||header||)
        jira_table = self._parse_jira_table(description)
        if jira_table:
            table_data.extend(jira_table)

        # Format 2: Markdown table
        markdown_table = self._parse_markdown_table(description)
        if markdown_table:
            table_data.extend(markdown_table)

        # Format 3: Simple field-value pairs
        field_value_pairs = self._parse_field_value_pairs(description)
        if field_value_pairs:
            table_data.extend(field_value_pairs)

        return table_data

    def _parse_jira_table(self, text: str) -> List[Dict[str, str]]:
        """Parse Jira table markup format."""
        tables = []

        # Find table patterns
        table_pattern = r'\|\|([^|]+)\|\|([^|]+)\|\|'
        matches = re.findall(table_pattern, text, re.MULTILINE)

        if matches:
            for field, value in matches:
                tables.append({
                    'field': field.strip(),
                    'value': value.strip()
                })

        return tables

    def _parse_markdown_table(self, text: str) -> List[Dict[str, str]]:
        """Parse Markdown table format."""
        tables = []

        # Find markdown table patterns
        lines = text.split('\n')
        in_table = False
        headers = []

        for line in lines:
            line = line.strip()

            # Check if this is a table row
            if '|' in line and line.startswith('|') and line.endswith('|'):
                cells = [cell.strip() for cell in line.split('|')[1:-1]]

                if not in_table:
                    # This might be the header row
                    headers = cells
                    in_table = True
                elif '---' in line:
                    # Skip separator row
                    continue
                else:
                    # Data row
                    if len(cells) >= 2 and len(headers) >= 2:
                        tables.append({
                            'field': cells[0],
                            'value': cells[1]
                        })
            else:
                in_table = False
                headers = []

        return tables

    def _parse_field_value_pairs(self, text: str) -> List[Dict[str, str]]:
        """Parse simple field: value pairs."""
        pairs = []

        # Pattern for field: value pairs
        pattern = r'^([^:]+):\s*(.+)$'

        for line in text.split('\n'):
            line = line.strip()
            match = re.match(pattern, line)
            if match:
                field, value = match.groups()
                pairs.append({
                    'field': field.strip(),
                    'value': value.strip()
                })

        return pairs

    def _parse_rules_from_table(
        self,
        table_data: List[Dict[str, str]]
    ) -> tuple[List[FirewallRule], List[str]]:
        """
        Parse firewall rules from extracted table data.

        Args:
            table_data: List of field-value dictionaries

        Returns:
            Tuple of (rules list, errors list)
        """
        rules = []
        errors = []

        if not table_data:
            errors.append("No table data found in issue description")
            return rules, errors

        # Group table data by rule (if multiple rules exist)
        rule_groups = self._group_table_data_by_rule(table_data)

        for i, rule_data in enumerate(rule_groups):
            try:
                rule = self._create_rule_from_data(rule_data)
                if rule:
                    rules.append(rule)
                else:
                    errors.append(f"Failed to create rule from data group {i+1}")
            except Exception as e:
                errors.append(f"Error parsing rule {i+1}: {str(e)}")

        return rules, errors

    def _group_table_data_by_rule(
        self,
        table_data: List[Dict[str, str]]
    ) -> List[Dict[str, str]]:
        """Group table data into individual rules."""
        # For now, assume all data represents a single rule
        # This can be enhanced to handle multiple rules

        if not table_data:
            return []

        # Combine all field-value pairs into a single rule
        rule_data = {}
        for item in table_data:
            field = item['field'].lower()
            value = item['value']
            rule_data[field] = value

        return [rule_data]

    def _create_rule_from_data(self, rule_data: Dict[str, str]) -> Optional[FirewallRule]:
        """Create a FirewallRule object from parsed data."""

        # Map fields to rule attributes
        mapped_data = {}

        for field, value in rule_data.items():
            field_type = self._classify_field(field)

            if field_type == FieldType.SOURCE:
                mapped_data['source'] = value
            elif field_type == FieldType.DESTINATION:
                mapped_data['destination'] = value
            elif field_type == FieldType.PORT:
                mapped_data['port'] = value
            elif field_type == FieldType.PROTOCOL:
                mapped_data['protocol'] = value
            elif field_type == FieldType.ACTION:
                mapped_data['action'] = value
            elif field_type == FieldType.JUSTIFICATION:
                mapped_data['justification'] = value
            elif field_type == FieldType.EXPIRATION:
                mapped_data['expiration'] = value
            elif field_type == FieldType.PRIORITY:
                mapped_data['priority'] = value

        # Validate required fields
        required_fields = ['source', 'destination', 'port', 'protocol']
        missing_fields = [f for f in required_fields if f not in mapped_data]

        if missing_fields:
            self.logger.warning(
                "Missing required fields for firewall rule",
                missing_fields=missing_fields,
                available_fields=list(mapped_data.keys())
            )
            return None

        return FirewallRule(
            source=mapped_data['source'],
            destination=mapped_data['destination'],
            port=mapped_data['port'],
            protocol=mapped_data['protocol'],
            action=mapped_data.get('action', 'allow'),
            justification=mapped_data.get('justification'),
            expiration=mapped_data.get('expiration'),
            priority=mapped_data.get('priority'),
            raw_data=rule_data
        )

    def _classify_field(self, field_name: str) -> FieldType:
        """Classify a field name into a FieldType."""
        field_lower = field_name.lower()

        for field_type, patterns in self.field_patterns.items():
            for pattern in patterns:
                if re.search(pattern, field_lower):
                    return field_type

        return FieldType.OTHER
