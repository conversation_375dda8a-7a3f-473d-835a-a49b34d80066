"""
Jira API client for retrieving and updating firewall change requests.
"""

from typing import Optional, Dict, Any, List
import requests
from requests.auth import HTTPBasicAuth
import json
import logging

from ..config.settings import settings
from ..utils.logger import get_logger


class JiraClient:
    """Client for interacting with Jira API using REST API directly."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.jira_settings = settings.jira
        self.session = requests.Session()
        self.session.auth = HTTPBasicAuth(
            self.jira_settings.username,
            self.jira_settings.api_token
        )
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        self._authenticated = False

    def connect(self) -> bool:
        """
        Test connection to Jira.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Test connection with myself endpoint
            response = self.session.get(
                f"{self.jira_settings.url}/rest/api/2/myself"
            )

            if response.status_code == 200:
                self._authenticated = True
                self.logger.info(
                    "Successfully connected to <PERSON><PERSON>",
                    server=self.jira_settings.url,
                    username=self.jira_settings.username
                )
                return True
            else:
                self.logger.error(
                    "Failed to connect to Jira",
                    status_code=response.status_code,
                    response=response.text,
                    server=self.jira_settings.url
                )
                return False

        except Exception as e:
            self.logger.error(
                "Failed to connect to Jira",
                error=str(e),
                server=self.jira_settings.url
            )
            return False

    def get_issue(self, issue_key: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a Jira issue by key.

        Args:
            issue_key: Jira issue key (e.g., 'FW-123')

        Returns:
            Issue data dictionary or None if not found
        """
        if not self._authenticated and not self.connect():
            return None

        try:
            response = self.session.get(
                f"{self.jira_settings.url}/rest/api/2/issue/{issue_key}",
                params={'expand': 'changelog'}
            )

            if response.status_code == 200:
                issue = response.json()

                issue_data = {
                    'key': issue['key'],
                    'summary': issue['fields'].get('summary', ''),
                    'description': issue['fields'].get('description', ''),
                    'status': issue['fields']['status']['name'],
                    'priority': issue['fields']['priority']['name'] if issue['fields'].get('priority') else None,
                    'assignee': issue['fields']['assignee']['displayName'] if issue['fields'].get('assignee') else None,
                    'reporter': issue['fields']['reporter']['displayName'] if issue['fields'].get('reporter') else None,
                    'created': issue['fields']['created'],
                    'updated': issue['fields']['updated'],
                    'comments': [
                        {
                            'author': comment['author']['displayName'],
                            'body': comment['body'],
                            'created': comment['created']
                        }
                        for comment in issue['fields'].get('comment', {}).get('comments', [])
                    ]
                }

                self.logger.info(
                    "Retrieved Jira issue",
                    issue_key=issue_key,
                    summary=issue_data['summary'],
                    status=issue_data['status']
                )

                return issue_data
            else:
                self.logger.error(
                    "Failed to retrieve Jira issue",
                    issue_key=issue_key,
                    status_code=response.status_code,
                    response=response.text
                )
                return None

        except Exception as e:
            self.logger.error(
                "Failed to retrieve Jira issue",
                issue_key=issue_key,
                error=str(e)
            )
            return None

    def add_comment(self, issue_key: str, comment_body: str) -> bool:
        """
        Add a comment to a Jira issue.

        Args:
            issue_key: Jira issue key
            comment_body: Comment text to add

        Returns:
            True if comment added successfully, False otherwise
        """
        if not self._authenticated and not self.connect():
            return False

        try:
            comment_data = {
                'body': comment_body
            }

            response = self.session.post(
                f"{self.jira_settings.url}/rest/api/2/issue/{issue_key}/comment",
                data=json.dumps(comment_data)
            )

            if response.status_code in [200, 201]:
                self.logger.info(
                    "Added comment to Jira issue",
                    issue_key=issue_key,
                    comment_length=len(comment_body)
                )
                return True
            else:
                self.logger.error(
                    "Failed to add comment to Jira issue",
                    issue_key=issue_key,
                    status_code=response.status_code,
                    response=response.text
                )
                return False

        except Exception as e:
            self.logger.error(
                "Failed to add comment to Jira issue",
                issue_key=issue_key,
                error=str(e)
            )
            return False

    def update_issue_field(
        self,
        issue_key: str,
        field_name: str,
        field_value: Any
    ) -> bool:
        """
        Update a specific field in a Jira issue.

        Args:
            issue_key: Jira issue key
            field_name: Name of the field to update
            field_value: New value for the field

        Returns:
            True if update successful, False otherwise
        """
        if not self._authenticated and not self.connect():
            return False

        try:
            update_data = {
                'fields': {
                    field_name: field_value
                }
            }

            response = self.session.put(
                f"{self.jira_settings.url}/rest/api/2/issue/{issue_key}",
                data=json.dumps(update_data)
            )

            if response.status_code in [200, 204]:
                self.logger.info(
                    "Updated Jira issue field",
                    issue_key=issue_key,
                    field_name=field_name
                )
                return True
            else:
                self.logger.error(
                    "Failed to update Jira issue field",
                    issue_key=issue_key,
                    field_name=field_name,
                    status_code=response.status_code,
                    response=response.text
                )
                return False

        except Exception as e:
            self.logger.error(
                "Failed to update Jira issue field",
                issue_key=issue_key,
                field_name=field_name,
                error=str(e)
            )
            return False

    def search_issues(self, jql: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """
        Search for issues using JQL.

        Args:
            jql: JQL query string
            max_results: Maximum number of results to return

        Returns:
            List of issue data dictionaries
        """
        if not self._authenticated and not self.connect():
            return []

        try:
            params = {
                'jql': jql,
                'maxResults': max_results,
                'fields': 'key,summary,status,created'
            }

            response = self.session.get(
                f"{self.jira_settings.url}/rest/api/2/search",
                params=params
            )

            if response.status_code == 200:
                search_results = response.json()

                results = []
                for issue in search_results.get('issues', []):
                    results.append({
                        'key': issue['key'],
                        'summary': issue['fields'].get('summary', ''),
                        'status': issue['fields']['status']['name'],
                        'created': issue['fields']['created']
                    })

                self.logger.info(
                    "Searched Jira issues",
                    jql=jql,
                    results_count=len(results)
                )

                return results
            else:
                self.logger.error(
                    "Failed to search Jira issues",
                    jql=jql,
                    status_code=response.status_code,
                    response=response.text
                )
                return []

        except Exception as e:
            self.logger.error(
                "Failed to search Jira issues",
                jql=jql,
                error=str(e)
            )
            return []
