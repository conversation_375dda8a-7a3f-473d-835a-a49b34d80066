"""
Jira API client for retrieving and updating firewall change requests.
"""

from typing import Optional, Dict, Any, List
import requests
from requests.auth import H<PERSON><PERSON><PERSON><PERSON>c<PERSON><PERSON>
from jira import JIRA
import structlog

from ..config.settings import settings
from ..utils.logger import get_logger


class JiraClient:
    """Client for interacting with Jira API."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.jira_settings = settings.jira
        self._client: Optional[JIRA] = None
    
    def connect(self) -> bool:
        """
        Establish connection to Jira.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self._client = JIRA(
                server=self.jira_settings.url,
                basic_auth=(
                    self.jira_settings.username,
                    self.jira_settings.api_token
                )
            )
            
            # Test connection
            self._client.myself()
            
            self.logger.info(
                "Successfully connected to Jira",
                server=self.jira_settings.url,
                username=self.jira_settings.username
            )
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to connect to <PERSON><PERSON>",
                error=str(e),
                server=self.jira_settings.url
            )
            return False
    
    def get_issue(self, issue_key: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a Jira issue by key.
        
        Args:
            issue_key: Jira issue key (e.g., 'FW-123')
        
        Returns:
            Issue data dictionary or None if not found
        """
        if not self._client:
            if not self.connect():
                return None
        
        try:
            issue = self._client.issue(issue_key, expand='changelog')
            
            issue_data = {
                'key': issue.key,
                'summary': issue.fields.summary,
                'description': issue.fields.description,
                'status': issue.fields.status.name,
                'priority': issue.fields.priority.name if issue.fields.priority else None,
                'assignee': issue.fields.assignee.displayName if issue.fields.assignee else None,
                'reporter': issue.fields.reporter.displayName if issue.fields.reporter else None,
                'created': issue.fields.created,
                'updated': issue.fields.updated,
                'comments': [
                    {
                        'author': comment.author.displayName,
                        'body': comment.body,
                        'created': comment.created
                    }
                    for comment in issue.fields.comment.comments
                ]
            }
            
            self.logger.info(
                "Retrieved Jira issue",
                issue_key=issue_key,
                summary=issue_data['summary'],
                status=issue_data['status']
            )
            
            return issue_data
            
        except Exception as e:
            self.logger.error(
                "Failed to retrieve Jira issue",
                issue_key=issue_key,
                error=str(e)
            )
            return None
    
    def add_comment(self, issue_key: str, comment_body: str) -> bool:
        """
        Add a comment to a Jira issue.
        
        Args:
            issue_key: Jira issue key
            comment_body: Comment text to add
        
        Returns:
            True if comment added successfully, False otherwise
        """
        if not self._client:
            if not self.connect():
                return False
        
        try:
            self._client.add_comment(issue_key, comment_body)
            
            self.logger.info(
                "Added comment to Jira issue",
                issue_key=issue_key,
                comment_length=len(comment_body)
            )
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to add comment to Jira issue",
                issue_key=issue_key,
                error=str(e)
            )
            return False
    
    def update_issue_field(
        self, 
        issue_key: str, 
        field_name: str, 
        field_value: Any
    ) -> bool:
        """
        Update a specific field in a Jira issue.
        
        Args:
            issue_key: Jira issue key
            field_name: Name of the field to update
            field_value: New value for the field
        
        Returns:
            True if update successful, False otherwise
        """
        if not self._client:
            if not self.connect():
                return False
        
        try:
            issue = self._client.issue(issue_key)
            issue.update(fields={field_name: field_value})
            
            self.logger.info(
                "Updated Jira issue field",
                issue_key=issue_key,
                field_name=field_name
            )
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to update Jira issue field",
                issue_key=issue_key,
                field_name=field_name,
                error=str(e)
            )
            return False
    
    def search_issues(self, jql: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """
        Search for issues using JQL.
        
        Args:
            jql: JQL query string
            max_results: Maximum number of results to return
        
        Returns:
            List of issue data dictionaries
        """
        if not self._client:
            if not self.connect():
                return []
        
        try:
            issues = self._client.search_issues(jql, maxResults=max_results)
            
            results = []
            for issue in issues:
                results.append({
                    'key': issue.key,
                    'summary': issue.fields.summary,
                    'status': issue.fields.status.name,
                    'created': issue.fields.created
                })
            
            self.logger.info(
                "Searched Jira issues",
                jql=jql,
                results_count=len(results)
            )
            
            return results
            
        except Exception as e:
            self.logger.error(
                "Failed to search Jira issues",
                jql=jql,
                error=str(e)
            )
            return []
