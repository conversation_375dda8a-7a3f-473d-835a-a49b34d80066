# Firewall Change Automation System

An automated system for analyzing Jira firewall change requests, validating against security standards, and providing recommendations using Tufin API integration.

## 🎯 Overview

This system automates the security review process for firewall change requests by:

- **Retrieving** firewall change data from Jira issues
- **Parsing** firewall rule tables from issue descriptions
- **Validating** rules against security standards
- **Analyzing** existing rules using Tufin API
- **Generating** security recommendations
- **Posting** results back to Jira automatically

## 🏗️ Architecture

```
firewall-automation/
├── src/
│   ├── main.py                    # CLI entry point
│   ├── workflow.py                # Main orchestration logic
│   ├── config/
│   │   ├── settings.py            # Configuration management
│   │   └── security_standards.json # Security policy definitions
│   ├── jira/
│   │   ├── client.py              # Jira API integration
│   │   └── parser.py              # Firewall table parsing
│   ├── tufin/
│   │   ├── client.py              # Tufin API integration
│   │   └── analyzer.py            # Rule analysis logic
│   ├── security/
│   │   ├── validator.py           # Security standards validation
│   │   └── risk_analyzer.py       # Risk assessment
│   └── utils/
│       ├── logger.py              # Logging utilities
│       └── helpers.py             # Common utilities
├── tests/                         # Unit tests
├── requirements.txt               # Python dependencies
├── azure-pipelines.yml           # Azure DevOps pipeline
└── README.md                      # This file
```

## 🚀 Quick Start

### Prerequisites

- Anaconda or Miniconda
- Python 3.11+ (via Anaconda)
- Access to Jira instance with API token
- Access to Tufin SecureTrack instance
- Azure DevOps (for pipeline execution)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd firewall-automation
   ```

2. **Create and activate conda environment:**
   ```bash
   # Option 1: Using environment.yml (recommended)
   conda env create -f environment.yml
   conda activate firewall-automation

   # Option 2: Manual installation
   conda create -n firewall-automation python=3.11
   conda activate firewall-automation
   conda install requests click urllib3 pandas jsonschema colorama pytest
   ```

3. **Install additional packages if needed:**
   ```bash
   # Optional: Install development tools
   conda install black flake8 pytest-mock pytest-cov

   # If python-dotenv is not available via conda
   pip install python-dotenv
   ```

4. **Configure environment variables:**
   ```bash
   # Copy example environment file
   cp .env.example .env

   # Edit .env with your credentials
   JIRA_URL=https://your-jira-instance.com
   JIRA_USERNAME=your-username
   JIRA_API_TOKEN=your-api-token
   TUFIN_URL=https://your-tufin-instance.com
   TUFIN_USERNAME=your-username
   TUFIN_PASSWORD=your-password
   ```

### Usage

#### Command Line

```bash
# Activate conda environment first
conda activate firewall-automation

# Analyze a specific Jira issue
python -m src.main --jira-issue FW-123

# Dry run (don't post comments)
python -m src.main --jira-issue FW-123 --dry-run

# Custom log level and output directory
python -m src.main --jira-issue FW-123 --log-level DEBUG --output-dir ./results
```

#### Azure DevOps Pipeline

1. **Import the pipeline:**
   - Use `azure-pipelines.yml` in your Azure DevOps project
   - Configure the required variables (see Configuration section)

2. **Run the pipeline:**
   - Trigger manually with Jira issue key parameter
   - Choose dry-run mode for testing

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `JIRA_URL` | Jira instance URL | Yes |
| `JIRA_USERNAME` | Jira username | Yes |
| `JIRA_API_TOKEN` | Jira API token | Yes |
| `TUFIN_URL` | Tufin SecureTrack URL | Yes |
| `TUFIN_USERNAME` | Tufin username | Yes |
| `TUFIN_PASSWORD` | Tufin password | Yes |
| `LOG_LEVEL` | Logging level (DEBUG, INFO, WARNING, ERROR) | No |
| `DRY_RUN` | Don't post comments to Jira | No |

### Security Standards

Security standards are defined in `src/config/security_standards.json`:

```json
{
  "prohibited_sources": {
    "wildcards": ["*", "any", "0.0.0.0/0", "::/0"]
  },
  "risky_protocols": {
    "high_risk": ["ssh", "telnet", "ftp", "tftp"],
    "medium_risk": ["http", "pop3", "imap"]
  },
  "dangerous_ports": {
    "prohibited": ["23", "69", "135"],
    "requires_justification": ["22", "21", "80"]
  }
}
```

## 📊 Supported Jira Table Formats

The system can parse firewall data from various table formats:

### Jira Table Markup
```
||Field||Value||
|Source|***********/24|
|Destination|**********|
|Port|443|
|Protocol|HTTPS|
```

### Markdown Table
```
| Field | Value |
|-------|-------|
| Source | ***********/24 |
| Destination | ********** |
| Port | 443 |
| Protocol | HTTPS |
```

### Field-Value Pairs
```
Source: ***********/24
Destination: **********
Port: 443
Protocol: HTTPS
```

## 🔍 Enhanced Security Validation

The system validates firewall rules against comprehensive security criteria based on industry best practices:

### Wildcard Detection
- Flags usage of `*`, `any`, `0.0.0.0/0`, `::/0`, `all`, `internet`
- Detects untrusted network sources (DMZ, guest, external)
- Recommends specific IP addresses/subnets

### Advanced Protocol Risk Assessment
- **Critical Risk:** Telnet, RSH, RLogin, TFTP, Finger, Echo services
- **High Risk:** FTP, SNMP v1/v2, NetBIOS, SMB, CIFS, NFS, X11
- **Medium Risk:** SSH, HTTP, SMTP, POP3, IMAP, LDAP, Kerberos
- **Legacy Protocols:** AppleTalk, IPX, NetBEUI, DECnet
- Suggests secure alternatives and compliance requirements

### Comprehensive Port Analysis
- **Prohibited Ports:** Telnet (23), TFTP (69), Echo services (7,9,11,13,15,17,19)
- **Critical Infrastructure:** DNS (53), Kerberos (88), LDAP (389), SMB (445)
- **Database Ports:** SQL Server (1433), Oracle (1521), MySQL (3306), PostgreSQL (5432)
- **Management Ports:** SNMP (161/162), IPMI (623), Web management (8080/8443)
- **Remote Access:** RDP (3389), VNC (5900-5902), SSH (22)
- **Port Range Limits:** Maximum 50 ports per range

### Suspicious Pattern Detection
- **Attack Ports:** 31337 (Elite), 4444 (Metasploit), 12345 (NetBus)
- **P2P Applications:** BitTorrent (6881-6889), eMule (4662)
- **Tor Network:** Relay (9001), SOCKS proxy (9050/9051)
- **Cryptocurrency:** Bitcoin (8333), Litecoin (9333)

### Advanced Justification Analysis
- **Minimum Length:** 20 characters required
- **Red Flag Detection:** "testing", "temporary", "workaround", "bypass"
- **Quality Assessment:** Business purpose, approval references
- **Emergency Keywords:** Require additional review

### Compliance Framework Validation
- **PCI DSS:** Prohibited unencrypted protocols, encryption requirements
- **HIPAA:** Data transmission encryption standards
- **SOX:** Financial system segregation requirements

### Network Security Patterns
- **RFC1918 External:** Private network external routing detection
- **Multicast/Broadcast:** Invalid destination address patterns
- **Network Zones:** DMZ, internal, external, management risk assessment

📖 **For detailed information, see:** `docs/security-standards-reference.md`

## 🔧 Tufin Integration

The system integrates with Tufin SecureTrack to:

- **Search existing rules** for conflicts
- **Analyze rule impact** on current policies
- **Identify overlapping rules** that need consolidation
- **Check policy compliance** against organizational standards

## 📈 Enhanced Risk Scoring

Risk scores are calculated based on comprehensive security criteria:

| Violation Category | Risk Score | Examples |
|-------------------|------------|----------|
| **Critical Violations** | 10 | Wildcard source, Critical protocols (Telnet), Prohibited ports |
| **High Risk** | 8-9 | Wildcard destination, High-risk protocols (FTP, SNMP), Database ports |
| **Medium Risk** | 5-7 | Medium-risk protocols (SSH, HTTP), Management ports, Legacy protocols |
| **Low Risk** | 3-4 | Weak justification, Missing justification, Suspicious ranges |
| **Informational** | 1-2 | Minor configuration issues |

### Detailed Risk Categories

| Violation Type | Risk Score | Severity |
|----------------|------------|----------|
| Wildcard Source | 10 | Critical |
| Critical Protocol (Telnet, RSH) | 10 | Critical |
| Prohibited Port | 10 | Critical |
| Wildcard Destination | 9 | High |
| Critical Infrastructure Port | 9 | High |
| Database Port | 8 | High |
| Suspicious Port (31337, 4444) | 8 | High |
| High-Risk Protocol (FTP, SNMP) | 8 | High |
| RFC1918 External Routing | 8 | High |
| Legacy Protocol | 7 | Medium |
| Management Port | 7 | Medium |
| P2P Port | 7 | Medium |
| Remote Access Port | 6 | Medium |
| Large Port Range | 6 | Medium |
| Multicast/Broadcast | 6 | Medium |
| Medium-Risk Protocol | 5 | Medium |
| Suspicious Range | 5 | Medium |
| Missing Justification | 4 | Low |
| Weak Justification | 3 | Low |

**Risk Score Multipliers:**
- **Network Zone:** External (2.0x), Management (1.8x), DMZ (1.5x), Internal (1.0x)
- **Time Factor:** Off-hours (1.2x), Maintenance window (0.8x)
- **Action Type:** Allow (1.0x), NAT (1.3x), Deny (0.5x), Log (0.3x)

**Total Risk Score Scale:**
- **1-2:** Minimal Risk - No significant security concerns
- **3-5:** Low Risk - Minor issues requiring attention
- **6-8:** Medium Risk - Moderate security concerns requiring review
- **9-15:** High Risk - Significant security issues requiring immediate attention
- **16+:** Critical Risk - Severe security violations requiring immediate remediation

## 🧪 Testing

```bash
# Activate conda environment
conda activate firewall-automation

# Run all tests
pytest

# Run with coverage (if pytest-cov is installed)
pytest --cov=src

# Run specific test file
pytest tests/test_security_validator.py

# Test setup validation
python test_setup.py
```

## 📝 Output

The system generates:

### Jira Comments
- Risk score and severity assessment
- Detailed security recommendations
- Action items for remediation

### Analysis Reports
- JSON files with complete analysis results
- Detailed violation breakdowns
- Tufin rule analysis results

### Logs
- Structured logging in JSON or text format
- Detailed execution traces
- Error handling and debugging information

## 🔒 Security Considerations

- **API Credentials:** Store securely in Azure Key Vault
- **Network Access:** Ensure connectivity to Jira and Tufin
- **Permissions:** Use least-privilege service accounts
- **Audit Trail:** All actions are logged for compliance

## 🚀 Azure DevOps Integration

### Pipeline Features
- **Manual Triggers:** Run on-demand with issue parameters
- **Parameter Validation:** Ensures valid Jira issue format
- **Artifact Publishing:** Saves all analysis results
- **Error Handling:** Graceful failure with detailed logs

### Required Variables
Configure these in Azure DevOps Library:
- `JIRA_URL`, `JIRA_USERNAME`, `JIRA_API_TOKEN`
- `TUFIN_URL`, `TUFIN_USERNAME`, `TUFIN_PASSWORD`

## 📞 Support

For issues and questions:
1. Check the logs in the output directory
2. Review the Azure DevOps pipeline execution
3. Validate API connectivity and credentials
4. Ensure Jira issue contains properly formatted firewall tables

## 🔄 Future Enhancements

- **Webhook Integration:** Automatic triggering on Jira updates
- **Multi-Rule Support:** Handle multiple firewall rules per issue
- **Custom Policies:** Organization-specific security standards
- **Dashboard Integration:** Real-time security metrics
- **Approval Workflows:** Automated approval routing based on risk scores
