# Firewall Change Automation System

An automated system for analyzing Jira firewall change requests, validating against security standards, and providing recommendations using Tufin API integration.

## 🎯 Overview

This system automates the security review process for firewall change requests by:

- **Retrieving** firewall change data from Jira issues
- **Parsing** firewall rule tables from issue descriptions
- **Validating** rules against security standards
- **Analyzing** existing rules using Tufin API
- **Generating** security recommendations
- **Posting** results back to Jira automatically

## 🏗️ Architecture

```
firewall-automation/
├── src/
│   ├── main.py                    # CLI entry point
│   ├── workflow.py                # Main orchestration logic
│   ├── config/
│   │   ├── settings.py            # Configuration management
│   │   └── security_standards.json # Security policy definitions
│   ├── jira/
│   │   ├── client.py              # Jira API integration
│   │   └── parser.py              # Firewall table parsing
│   ├── tufin/
│   │   ├── client.py              # Tufin API integration
│   │   └── analyzer.py            # Rule analysis logic
│   ├── security/
│   │   ├── validator.py           # Security standards validation
│   │   └── risk_analyzer.py       # Risk assessment
│   └── utils/
│       ├── logger.py              # Logging utilities
│       └── helpers.py             # Common utilities
├── tests/                         # Unit tests
├── requirements.txt               # Python dependencies
├── azure-pipelines.yml           # Azure DevOps pipeline
└── README.md                      # This file
```

## 🚀 Quick Start

### Prerequisites

- Anaconda or Miniconda
- Python 3.11+ (via Anaconda)
- Access to Jira instance with API token
- Access to Tufin SecureTrack instance
- Azure DevOps (for pipeline execution)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd firewall-automation
   ```

2. **Create and activate conda environment:**
   ```bash
   # Option 1: Using environment.yml (recommended)
   conda env create -f environment.yml
   conda activate firewall-automation

   # Option 2: Manual installation
   conda create -n firewall-automation python=3.11
   conda activate firewall-automation
   conda install requests click urllib3 pandas jsonschema colorama pytest
   ```

3. **Install additional packages if needed:**
   ```bash
   # Optional: Install development tools
   conda install black flake8 pytest-mock pytest-cov

   # If python-dotenv is not available via conda
   pip install python-dotenv
   ```

4. **Configure environment variables:**
   ```bash
   # Copy example environment file
   cp .env.example .env

   # Edit .env with your credentials
   JIRA_URL=https://your-jira-instance.com
   JIRA_USERNAME=your-username
   JIRA_API_TOKEN=your-api-token
   TUFIN_URL=https://your-tufin-instance.com
   TUFIN_USERNAME=your-username
   TUFIN_PASSWORD=your-password
   ```

### Usage

#### Command Line

```bash
# Activate conda environment first
conda activate firewall-automation

# Analyze a specific Jira issue
python -m src.main --jira-issue FW-123

# Dry run (don't post comments)
python -m src.main --jira-issue FW-123 --dry-run

# Custom log level and output directory
python -m src.main --jira-issue FW-123 --log-level DEBUG --output-dir ./results
```

#### Azure DevOps Pipeline

1. **Import the pipeline:**
   - Use `azure-pipelines.yml` in your Azure DevOps project
   - Configure the required variables (see Configuration section)

2. **Run the pipeline:**
   - Trigger manually with Jira issue key parameter
   - Choose dry-run mode for testing

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `JIRA_URL` | Jira instance URL | Yes |
| `JIRA_USERNAME` | Jira username | Yes |
| `JIRA_API_TOKEN` | Jira API token | Yes |
| `TUFIN_URL` | Tufin SecureTrack URL | Yes |
| `TUFIN_USERNAME` | Tufin username | Yes |
| `TUFIN_PASSWORD` | Tufin password | Yes |
| `LOG_LEVEL` | Logging level (DEBUG, INFO, WARNING, ERROR) | No |
| `DRY_RUN` | Don't post comments to Jira | No |

### Security Standards

Security standards are defined in `src/config/security_standards.json`:

```json
{
  "prohibited_sources": {
    "wildcards": ["*", "any", "0.0.0.0/0", "::/0"]
  },
  "risky_protocols": {
    "high_risk": ["ssh", "telnet", "ftp", "tftp"],
    "medium_risk": ["http", "pop3", "imap"]
  },
  "dangerous_ports": {
    "prohibited": ["23", "69", "135"],
    "requires_justification": ["22", "21", "80"]
  }
}
```

## 📊 Supported Jira Table Formats

The system can parse firewall data from various table formats:

### Jira Table Markup
```
||Field||Value||
|Source|***********/24|
|Destination|**********|
|Port|443|
|Protocol|HTTPS|
```

### Markdown Table
```
| Field | Value |
|-------|-------|
| Source | ***********/24 |
| Destination | ********** |
| Port | 443 |
| Protocol | HTTPS |
```

### Field-Value Pairs
```
Source: ***********/24
Destination: **********
Port: 443
Protocol: HTTPS
```

## 🔍 Security Validation

The system validates firewall rules against multiple criteria:

### Wildcard Detection
- Flags usage of `*`, `any`, `0.0.0.0/0`
- Recommends specific IP addresses/subnets

### Protocol Risk Assessment
- **High Risk:** SSH, Telnet, FTP, TFTP
- **Medium Risk:** HTTP, POP3, IMAP
- Suggests secure alternatives

### Port Analysis
- **Prohibited Ports:** 23 (Telnet), 69 (TFTP), 135 (RPC)
- **Requires Justification:** 22 (SSH), 21 (FTP), 80 (HTTP)
- **Port Range Limits:** Maximum 100 ports per range

### Business Justification
- Validates presence of business justification
- Checks for required keywords

## 🔧 Tufin Integration

The system integrates with Tufin SecureTrack to:

- **Search existing rules** for conflicts
- **Analyze rule impact** on current policies
- **Identify overlapping rules** that need consolidation
- **Check policy compliance** against organizational standards

## 📈 Risk Scoring

Risk scores are calculated based on:

| Violation Type | Risk Score |
|----------------|------------|
| Wildcard Source | 10 |
| Wildcard Destination | 8 |
| High-Risk Protocol | 9 |
| Medium-Risk Protocol | 5 |
| Dangerous Port | 7 |
| Large Port Range | 6 |
| Missing Justification | 4 |

**Total Risk Score Scale:**
- 1-2: Minimal Risk
- 3-5: Low Risk
- 6-7: Medium Risk
- 8-9: High Risk
- 10: Critical Risk

## 🧪 Testing

```bash
# Activate conda environment
conda activate firewall-automation

# Run all tests
pytest

# Run with coverage (if pytest-cov is installed)
pytest --cov=src

# Run specific test file
pytest tests/test_security_validator.py

# Test setup validation
python test_setup.py
```

## 📝 Output

The system generates:

### Jira Comments
- Risk score and severity assessment
- Detailed security recommendations
- Action items for remediation

### Analysis Reports
- JSON files with complete analysis results
- Detailed violation breakdowns
- Tufin rule analysis results

### Logs
- Structured logging in JSON or text format
- Detailed execution traces
- Error handling and debugging information

## 🔒 Security Considerations

- **API Credentials:** Store securely in Azure Key Vault
- **Network Access:** Ensure connectivity to Jira and Tufin
- **Permissions:** Use least-privilege service accounts
- **Audit Trail:** All actions are logged for compliance

## 🚀 Azure DevOps Integration

### Pipeline Features
- **Manual Triggers:** Run on-demand with issue parameters
- **Parameter Validation:** Ensures valid Jira issue format
- **Artifact Publishing:** Saves all analysis results
- **Error Handling:** Graceful failure with detailed logs

### Required Variables
Configure these in Azure DevOps Library:
- `JIRA_URL`, `JIRA_USERNAME`, `JIRA_API_TOKEN`
- `TUFIN_URL`, `TUFIN_USERNAME`, `TUFIN_PASSWORD`

## 📞 Support

For issues and questions:
1. Check the logs in the output directory
2. Review the Azure DevOps pipeline execution
3. Validate API connectivity and credentials
4. Ensure Jira issue contains properly formatted firewall tables

## 🔄 Future Enhancements

- **Webhook Integration:** Automatic triggering on Jira updates
- **Multi-Rule Support:** Handle multiple firewall rules per issue
- **Custom Policies:** Organization-specific security standards
- **Dashboard Integration:** Real-time security metrics
- **Approval Workflows:** Automated approval routing based on risk scores
