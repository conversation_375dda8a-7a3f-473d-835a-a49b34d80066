#!/bin/bash
# Setup script for Firewall Automation using Conda
# This script sets up the conda environment and dependencies

set -e  # Exit on any error

echo "🚀 Setting up Firewall Automation with Conda"
echo "=============================================="

# Check if conda is installed
if ! command -v conda &> /dev/null; then
    echo "❌ Conda is not installed. Please install Anaconda or Miniconda first."
    echo "   Download from: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

echo "✅ Conda found: $(conda --version)"

# Check if environment.yml exists
if [ ! -f "environment.yml" ]; then
    echo "❌ environment.yml not found. Make sure you're in the firewall-automation directory."
    exit 1
fi

# Create conda environment
echo "📦 Creating conda environment from environment.yml..."
if conda env list | grep -q "firewall-automation"; then
    echo "⚠️  Environment 'firewall-automation' already exists."
    read -p "Do you want to update it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔄 Updating existing environment..."
        conda env update -f environment.yml
    else
        echo "ℹ️  Skipping environment creation."
    fi
else
    conda env create -f environment.yml
fi

echo "✅ Conda environment created/updated successfully"

# Activate environment and test
echo "🧪 Testing environment setup..."
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate firewall-automation

# Test Python imports
python -c "
import sys
print(f'✅ Python {sys.version}')

try:
    import requests, click, pandas, json
    print('✅ Core dependencies imported successfully')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)

try:
    from src.config.settings import settings
    print('✅ Application modules imported successfully')
except ImportError as e:
    print(f'❌ Application import error: {e}')
    print('   Make sure you are in the firewall-automation directory')
    sys.exit(1)
"

# Check for .env file
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found."
    if [ -f ".env.example" ]; then
        echo "📋 Copying .env.example to .env..."
        cp .env.example .env
        echo "✅ .env file created. Please edit it with your actual credentials."
    else
        echo "❌ .env.example not found either. Please create .env manually."
    fi
else
    echo "✅ .env file found"
fi

# Create output directory
mkdir -p output/logs output/reports
echo "✅ Output directories created"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Activate the environment: conda activate firewall-automation"
echo "2. Edit .env file with your API credentials"
echo "3. Test the setup: python test_setup.py"
echo "4. Run analysis: python -m src.main --jira-issue YOUR-ISSUE-KEY --dry-run"
echo ""
echo "For more information, see README.md"
