"""
Main workflow orchestrator for firewall change automation.
"""

import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path
from datetime import datetime

from .jira import JiraClient, FirewallTableParser
from .security import SecurityValidator
from .tufin import TufinClient
from .utils.logger import get_logger


@dataclass
class Recommendation:
    """Represents a security recommendation."""
    title: str
    description: str
    severity: str
    category: str
    action_required: bool


@dataclass
class WorkflowResult:
    """Result of the firewall analysis workflow."""
    success: bool
    jira_issue: str
    recommendations: List[Recommendation]
    risk_score: int
    error_message: Optional[str] = None
    tufin_analysis: Optional[Dict[str, Any]] = None
    validation_results: Optional[List[Any]] = None


class FirewallAnalysisWorkflow:
    """Main workflow for analyzing firewall change requests."""

    def __init__(
        self,
        jira_issue: str,
        output_dir: str,
        dry_run: bool = False,
        logger = None
    ):
        self.jira_issue = jira_issue
        self.output_dir = Path(output_dir)
        self.dry_run = dry_run
        self.logger = logger or get_logger(__name__)

        # Initialize clients
        self.jira_client = JiraClient()
        self.tufin_client = TufinClient()
        self.security_validator = SecurityValidator()
        self.parser = FirewallTableParser()

    def run(self) -> WorkflowResult:
        """
        Execute the complete firewall analysis workflow.

        Returns:
            WorkflowResult with analysis results
        """
        self.logger.info(
            "Starting firewall analysis workflow",
            jira_issue=self.jira_issue,
            dry_run=self.dry_run
        )

        try:
            # Step 1: Retrieve Jira issue
            issue_data = self._retrieve_jira_issue()
            if not issue_data:
                return WorkflowResult(
                    success=False,
                    jira_issue=self.jira_issue,
                    recommendations=[],
                    risk_score=0,
                    error_message="Failed to retrieve Jira issue"
                )

            # Step 2: Parse firewall change request
            change_request = self._parse_firewall_request(issue_data)
            if not change_request.rules:
                return WorkflowResult(
                    success=False,
                    jira_issue=self.jira_issue,
                    recommendations=[],
                    risk_score=0,
                    error_message="No firewall rules found in issue description"
                )

            # Step 3: Validate against security standards
            validation_results = self._validate_security_standards(change_request)

            # Step 4: Analyze with Tufin
            tufin_analysis = self._analyze_with_tufin(change_request)

            # Step 5: Generate recommendations
            recommendations = self._generate_recommendations(
                validation_results,
                tufin_analysis
            )

            # Step 6: Calculate risk score
            risk_score = self._calculate_risk_score(validation_results)

            # Step 7: Save results
            self._save_results(
                change_request,
                validation_results,
                tufin_analysis,
                recommendations,
                risk_score
            )

            # Step 8: Post to Jira (if not dry run)
            if not self.dry_run:
                self._post_jira_comments(recommendations, risk_score)

            return WorkflowResult(
                success=True,
                jira_issue=self.jira_issue,
                recommendations=recommendations,
                risk_score=risk_score,
                tufin_analysis=tufin_analysis,
                validation_results=validation_results
            )

        except Exception as e:
            self.logger.exception("Workflow execution failed")
            return WorkflowResult(
                success=False,
                jira_issue=self.jira_issue,
                recommendations=[],
                risk_score=0,
                error_message=str(e)
            )

    def _retrieve_jira_issue(self) -> Optional[Dict[str, Any]]:
        """Retrieve issue data from Jira."""
        self.logger.info("Retrieving Jira issue", issue_key=self.jira_issue)

        if not self.jira_client.connect():
            self.logger.error("Failed to connect to Jira")
            return None

        return self.jira_client.get_issue(self.jira_issue)

    def _parse_firewall_request(self, issue_data: Dict[str, Any]):
        """Parse firewall change request from issue data."""
        self.logger.info("Parsing firewall change request")
        return self.parser.parse_issue(issue_data)

    def _validate_security_standards(self, change_request):
        """Validate rules against security standards."""
        self.logger.info("Validating against security standards")
        return self.security_validator.validate_request(change_request)

    def _analyze_with_tufin(self, change_request) -> Dict[str, Any]:
        """Analyze rules with Tufin API."""
        self.logger.info("Analyzing with Tufin")

        if not self.tufin_client.authenticate():
            self.logger.warning("Failed to authenticate with Tufin, skipping analysis")
            return {}

        analysis_results = {}

        for i, rule in enumerate(change_request.rules):
            rule_analysis = self.tufin_client.analyze_rule_impact(
                source=rule.source,
                destination=rule.destination,
                port=rule.port,
                protocol=rule.protocol
            )

            analysis_results[f"rule_{i+1}"] = rule_analysis

        return analysis_results

    def _generate_recommendations(
        self,
        validation_results: List[Any],
        tufin_analysis: Dict[str, Any]
    ) -> List[Recommendation]:
        """Generate security recommendations."""
        self.logger.info("Generating recommendations")

        recommendations = []

        # Process validation results
        for result in validation_results:
            for violation in result.violations:
                recommendations.append(Recommendation(
                    title=f"{violation.type.value.replace('_', ' ').title()} Violation",
                    description=violation.message,
                    severity=violation.severity.value,
                    category="security_standards",
                    action_required=violation.severity.value in ['high', 'critical']
                ))

        # Process Tufin analysis
        for rule_key, analysis in tufin_analysis.items():
            for conflict in analysis.get('conflicts', []):
                recommendations.append(Recommendation(
                    title=f"Rule Conflict: {conflict['type'].replace('_', ' ').title()}",
                    description=conflict['message'],
                    severity=conflict['severity'],
                    category="rule_analysis",
                    action_required=conflict['severity'] in ['high', 'critical']
                ))

        # Add general recommendations
        if not recommendations:
            recommendations.append(Recommendation(
                title="No Security Issues Found",
                description="The firewall change request appears to comply with security standards.",
                severity="info",
                category="general",
                action_required=False
            ))

        return recommendations

    def _calculate_risk_score(self, validation_results: List[Any]) -> int:
        """Calculate overall risk score."""
        total_score = 0

        for result in validation_results:
            total_score += result.total_risk_score

        # Normalize to 1-10 scale
        if total_score == 0:
            return 1
        elif total_score <= 5:
            return min(3, total_score)
        elif total_score <= 15:
            return min(6, 3 + (total_score - 5) // 2)
        else:
            return min(10, 6 + (total_score - 15) // 5)

    def _save_results(
        self,
        change_request,
        validation_results,
        tufin_analysis,
        recommendations,
        risk_score
    ):
        """Save analysis results to files."""
        self.logger.info("Saving analysis results")

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Save detailed results
        results = {
            'jira_issue': self.jira_issue,
            'timestamp': datetime.now().isoformat(),
            'risk_score': risk_score,
            'change_request': {
                'issue_key': change_request.issue_key,
                'summary': change_request.summary,
                'rules_count': len(change_request.rules),
                'parsing_errors': change_request.parsing_errors
            },
            'validation_results': [
                {
                    'rule_index': i,
                    'violations_count': len(result.violations),
                    'risk_score': result.total_risk_score,
                    'is_compliant': result.is_compliant,
                    'violations': [
                        {
                            'type': v.type.value,
                            'severity': v.severity.value,
                            'message': v.message,
                            'field': v.field,
                            'value': v.value,
                            'recommendation': v.recommendation,
                            'risk_score': v.risk_score
                        }
                        for v in result.violations
                    ]
                }
                for i, result in enumerate(validation_results)
            ],
            'tufin_analysis': tufin_analysis,
            'recommendations': [
                {
                    'title': r.title,
                    'description': r.description,
                    'severity': r.severity,
                    'category': r.category,
                    'action_required': r.action_required
                }
                for r in recommendations
            ]
        }

        # Save to JSON file
        results_file = self.output_dir / f"analysis_results_{self.jira_issue}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)

        self.logger.info("Results saved", file=str(results_file))

    def _post_jira_comments(self, recommendations: List[Recommendation], risk_score: int):
        """Post recommendations as comments to Jira issue."""
        self.logger.info("Posting comments to Jira")

        # Format comment
        comment_lines = [
            "🔒 *Automated Firewall Security Analysis*",
            "",
            f"📊 *Risk Score:* {risk_score}/10",
            "",
            "🔍 *Security Recommendations:*"
        ]

        if not recommendations:
            comment_lines.append("✅ No security issues identified.")
        else:
            for i, rec in enumerate(recommendations, 1):
                severity_emoji = {
                    'critical': '🚨',
                    'high': '⚠️',
                    'medium': '⚡',
                    'low': 'ℹ️',
                    'info': '✅'
                }.get(rec.severity, 'ℹ️')

                comment_lines.extend([
                    f"{i}. {severity_emoji} *{rec.title}*",
                    f"   {rec.description}",
                    ""
                ])

        comment_lines.extend([
            "",
            "---",
            f"_Analysis generated automatically on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}_"
        ])

        comment_body = "\n".join(comment_lines)

        success = self.jira_client.add_comment(self.jira_issue, comment_body)

        if success:
            self.logger.info("Successfully posted comment to Jira")
        else:
            self.logger.error("Failed to post comment to Jira")
