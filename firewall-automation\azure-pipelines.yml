# Azure DevOps Pipeline for Firewall Change Automation
# This pipeline analyzes Jira firewall change requests and provides security recommendations

trigger: none  # Manual trigger only

parameters:
- name: jiraIssue
  displayName: 'Jira Issue Key'
  type: string
  default: ''

- name: dryRun
  displayName: 'Dry Run (do not post comments)'
  type: boolean
  default: true

- name: logLevel
  displayName: 'Log Level'
  type: string
  default: 'INFO'
  values:
  - DEBUG
  - INFO
  - WARNING
  - ERROR

variables:
  pythonVersion: '3.11'
  workingDirectory: '$(System.DefaultWorkingDirectory)/firewall-automation'
  condaEnvironment: 'firewall-automation'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Validate
  displayName: 'Validate Input Parameters'
  jobs:
  - job: ValidateInputs
    displayName: 'Validate Input Parameters'
    steps:
    - script: |
        if [ -z "${{ parameters.jiraIssue }}" ]; then
          echo "##vso[task.logissue type=error]Jira Issue Key is required"
          exit 1
        fi

        if [[ ! "${{ parameters.jiraIssue }}" =~ ^[A-Z]+-[0-9]+$ ]]; then
          echo "##vso[task.logissue type=error]Invalid Jira Issue Key format. Expected format: PROJECT-123"
          exit 1
        fi

        echo "✅ Input validation passed"
        echo "Jira Issue: ${{ parameters.jiraIssue }}"
        echo "Dry Run: ${{ parameters.dryRun }}"
        echo "Log Level: ${{ parameters.logLevel }}"
      displayName: 'Validate Parameters'

- stage: Setup
  displayName: 'Setup Environment'
  dependsOn: Validate
  jobs:
  - job: SetupConda
    displayName: 'Setup Conda Environment'
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '$(pythonVersion)'
        addToPath: true
        architecture: 'x64'
      displayName: 'Use Python $(pythonVersion)'

    - script: |
        # Install miniconda if not available
        if ! command -v conda &> /dev/null; then
          wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh
          bash miniconda.sh -b -p $HOME/miniconda
          export PATH="$HOME/miniconda/bin:$PATH"
          conda init bash
        fi

        # Create conda environment
        conda env create -f environment.yml
      workingDirectory: '$(workingDirectory)'
      displayName: 'Create Conda Environment'

    - script: |
        # Activate environment and verify installation
        source activate $(condaEnvironment)
        python -c "
        from src.config.settings import settings
        print('✅ Configuration module loaded successfully')
        print('✅ All dependencies installed correctly')
        "
      workingDirectory: '$(workingDirectory)'
      displayName: 'Verify Installation'

- stage: Analysis
  displayName: 'Firewall Analysis'
  dependsOn: Setup
  jobs:
  - job: RunAnalysis
    displayName: 'Run Firewall Change Analysis'
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '$(pythonVersion)'
        addToPath: true
        architecture: 'x64'
      displayName: 'Use Python $(pythonVersion)'

    - script: |
        # Activate conda environment
        source activate $(condaEnvironment)
      workingDirectory: '$(workingDirectory)'
      displayName: 'Activate Conda Environment'

    - script: |
        mkdir -p output/logs
        mkdir -p output/reports
      workingDirectory: '$(workingDirectory)'
      displayName: 'Create Output Directories'

    - script: |
        source activate $(condaEnvironment)
        python -m src.main \
          --jira-issue "${{ parameters.jiraIssue }}" \
          --log-level "${{ parameters.logLevel }}" \
          --output-dir "./output" \
          ${{ eq(parameters.dryRun, true) && '--dry-run' || '' }}
      workingDirectory: '$(workingDirectory)'
      env:
        JIRA_URL: $(JIRA_URL)
        JIRA_USERNAME: $(JIRA_USERNAME)
        JIRA_API_TOKEN: $(JIRA_API_TOKEN)
        TUFIN_URL: $(TUFIN_URL)
        TUFIN_USERNAME: $(TUFIN_USERNAME)
        TUFIN_PASSWORD: $(TUFIN_PASSWORD)
      displayName: 'Run Firewall Analysis'
      continueOnError: true

    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(workingDirectory)/output'
        artifactName: 'firewall-analysis-results'
        publishLocation: 'Container'
      displayName: 'Publish Analysis Results'
      condition: always()

    - script: |
        echo "## 📊 Firewall Analysis Summary" >> $(Agent.TempDirectory)/summary.md
        echo "" >> $(Agent.TempDirectory)/summary.md
        echo "**Jira Issue:** ${{ parameters.jiraIssue }}" >> $(Agent.TempDirectory)/summary.md
        echo "**Analysis Date:** $(date)" >> $(Agent.TempDirectory)/summary.md
        echo "**Dry Run:** ${{ parameters.dryRun }}" >> $(Agent.TempDirectory)/summary.md
        echo "" >> $(Agent.TempDirectory)/summary.md

        if [ -f "output/logs/firewall_automation_*.log" ]; then
          echo "**Log Files:** Available in artifacts" >> $(Agent.TempDirectory)/summary.md
        fi

        if [ -f "output/reports/*.json" ]; then
          echo "**Reports:** Available in artifacts" >> $(Agent.TempDirectory)/summary.md
        fi

        echo "" >> $(Agent.TempDirectory)/summary.md
        echo "📁 **Artifacts:** Check the published artifacts for detailed logs and reports" >> $(Agent.TempDirectory)/summary.md
      workingDirectory: '$(workingDirectory)'
      displayName: 'Generate Summary'
      condition: always()

    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(Agent.TempDirectory)/summary.md'
        artifactName: 'analysis-summary'
        publishLocation: 'Container'
      displayName: 'Publish Summary'
      condition: always()

- stage: Reporting
  displayName: 'Generate Reports'
  dependsOn: Analysis
  condition: always()
  jobs:
  - job: GenerateReports
    displayName: 'Generate Analysis Reports'
    steps:
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'firewall-analysis-results'
        downloadPath: '$(System.ArtifactsDirectory)'
      displayName: 'Download Analysis Results'

    - script: |
        echo "=== Firewall Change Analysis Report ==="
        echo "Issue: ${{ parameters.jiraIssue }}"
        echo "Date: $(date)"
        echo "Dry Run: ${{ parameters.dryRun }}"
        echo ""

        if [ -d "$(System.ArtifactsDirectory)/firewall-analysis-results" ]; then
          echo "📁 Analysis artifacts generated successfully"
          ls -la "$(System.ArtifactsDirectory)/firewall-analysis-results"
        else
          echo "⚠️  No analysis artifacts found"
        fi

        echo ""
        echo "🔍 For detailed results, check the published artifacts"
      displayName: 'Display Report Summary'

# Pipeline completion notification
- stage: Notification
  displayName: 'Send Notifications'
  dependsOn:
  - Analysis
  - Reporting
  condition: always()
  jobs:
  - job: SendNotification
    displayName: 'Send Completion Notification'
    steps:
    - script: |
        echo "🎯 Firewall analysis pipeline completed"
        echo "Issue: ${{ parameters.jiraIssue }}"
        echo "Status: $(Agent.JobStatus)"

        if [ "${{ parameters.dryRun }}" = "true" ]; then
          echo "ℹ️  This was a dry run - no comments were posted to Jira"
        else
          echo "✅ Analysis results have been posted to the Jira issue"
        fi
      displayName: 'Pipeline Completion Summary'
