"""
Risk analysis module for firewall change requests.
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..jira.parser import FirewallRule, FirewallChangeRequest
from .validator import ValidationResult
from ..utils.logger import get_logger


class RiskLevel(Enum):
    """Risk level classifications."""
    MINIMAL = "minimal"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskFactor:
    """Represents a specific risk factor."""
    name: str
    score: int
    description: str
    category: str
    mitigation: Optional[str] = None


@dataclass
class RiskAssessment:
    """Complete risk assessment for a firewall change request."""
    request_key: str
    total_risk_score: int
    risk_level: RiskLevel
    risk_factors: List[RiskFactor]
    recommendations: List[str]
    compliance_issues: List[str]
    network_impact: str
    business_impact: str


class RiskAnalyzer:
    """Analyzes overall risk for firewall change requests."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def analyze_request(
        self, 
        request: FirewallChangeRequest,
        validation_results: List[ValidationResult],
        tufin_analysis: Optional[Dict[str, Any]] = None
    ) -> RiskAssessment:
        """
        Perform comprehensive risk analysis on a firewall change request.
        
        Args:
            request: FirewallChangeRequest to analyze
            validation_results: Security validation results
            tufin_analysis: Optional Tufin analysis results
        
        Returns:
            RiskAssessment with comprehensive risk analysis
        """
        self.logger.info(
            "Starting risk analysis",
            request_key=request.issue_key,
            rules_count=len(request.rules)
        )
        
        # Calculate total risk score
        total_risk_score = sum(result.total_risk_score for result in validation_results)
        
        # Determine risk level
        risk_level = self._determine_risk_level(total_risk_score)
        
        # Extract risk factors
        risk_factors = self._extract_risk_factors(validation_results, tufin_analysis)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            validation_results, 
            tufin_analysis, 
            risk_level
        )
        
        # Identify compliance issues
        compliance_issues = self._identify_compliance_issues(validation_results)
        
        # Assess network impact
        network_impact = self._assess_network_impact(request, validation_results)
        
        # Assess business impact
        business_impact = self._assess_business_impact(request, risk_level)
        
        assessment = RiskAssessment(
            request_key=request.issue_key,
            total_risk_score=total_risk_score,
            risk_level=risk_level,
            risk_factors=risk_factors,
            recommendations=recommendations,
            compliance_issues=compliance_issues,
            network_impact=network_impact,
            business_impact=business_impact
        )
        
        self.logger.info(
            "Risk analysis completed",
            request_key=request.issue_key,
            total_risk_score=total_risk_score,
            risk_level=risk_level.value,
            risk_factors_count=len(risk_factors)
        )
        
        return assessment
    
    def _determine_risk_level(self, total_risk_score: int) -> RiskLevel:
        """Determine risk level based on total score."""
        if total_risk_score >= 20:
            return RiskLevel.CRITICAL
        elif total_risk_score >= 15:
            return RiskLevel.HIGH
        elif total_risk_score >= 8:
            return RiskLevel.MEDIUM
        elif total_risk_score >= 3:
            return RiskLevel.LOW
        else:
            return RiskLevel.MINIMAL
    
    def _extract_risk_factors(
        self, 
        validation_results: List[ValidationResult],
        tufin_analysis: Optional[Dict[str, Any]]
    ) -> List[RiskFactor]:
        """Extract individual risk factors from validation results."""
        risk_factors = []
        
        # Extract from validation results
        for result in validation_results:
            for violation in result.violations:
                risk_factors.append(RiskFactor(
                    name=violation.type.value.replace('_', ' ').title(),
                    score=violation.risk_score,
                    description=violation.message,
                    category="Security Standards",
                    mitigation=violation.recommendation
                ))
        
        # Extract from Tufin analysis
        if tufin_analysis:
            for rule_key, analysis in tufin_analysis.items():
                for conflict in analysis.get('conflicts', []):
                    risk_factors.append(RiskFactor(
                        name=f"Rule Conflict: {conflict['type'].replace('_', ' ').title()}",
                        score=self._get_conflict_score(conflict['severity']),
                        description=conflict['message'],
                        category="Rule Analysis",
                        mitigation=conflict.get('recommendation')
                    ))
        
        return risk_factors
    
    def _get_conflict_score(self, severity: str) -> int:
        """Get risk score for Tufin conflict severity."""
        severity_scores = {
            'critical': 10,
            'high': 8,
            'medium': 5,
            'low': 3
        }
        return severity_scores.get(severity.lower(), 3)
    
    def _generate_recommendations(
        self,
        validation_results: List[ValidationResult],
        tufin_analysis: Optional[Dict[str, Any]],
        risk_level: RiskLevel
    ) -> List[str]:
        """Generate risk-based recommendations."""
        recommendations = []
        
        # Risk level specific recommendations
        if risk_level == RiskLevel.CRITICAL:
            recommendations.extend([
                "🚨 CRITICAL RISK: This change requires immediate security review",
                "Consider rejecting this request until security issues are resolved",
                "Implement additional monitoring if change must proceed"
            ])
        elif risk_level == RiskLevel.HIGH:
            recommendations.extend([
                "⚠️ HIGH RISK: Requires security team approval before implementation",
                "Consider implementing additional security controls",
                "Schedule change during maintenance window with rollback plan"
            ])
        elif risk_level == RiskLevel.MEDIUM:
            recommendations.extend([
                "⚡ MEDIUM RISK: Review and address security concerns",
                "Consider implementing monitoring for this change",
                "Ensure proper documentation and approval"
            ])
        
        # Extract specific recommendations from validation results
        for result in validation_results:
            recommendations.extend(result.recommendations)
        
        # Extract recommendations from Tufin analysis
        if tufin_analysis:
            for analysis in tufin_analysis.values():
                recommendations.extend(analysis.get('recommendations', []))
        
        # Remove duplicates while preserving order
        seen = set()
        unique_recommendations = []
        for rec in recommendations:
            if rec not in seen:
                seen.add(rec)
                unique_recommendations.append(rec)
        
        return unique_recommendations
    
    def _identify_compliance_issues(
        self, 
        validation_results: List[ValidationResult]
    ) -> List[str]:
        """Identify compliance framework violations."""
        compliance_issues = []
        
        for result in validation_results:
            for violation in result.violations:
                # Check for compliance-related violations
                if 'compliance' in violation.type.value.lower():
                    compliance_issues.append(
                        f"{violation.type.value}: {violation.message}"
                    )
                elif violation.severity.value in ['critical', 'high']:
                    # High severity violations may impact compliance
                    compliance_issues.append(
                        f"Potential compliance impact: {violation.message}"
                    )
        
        return compliance_issues
    
    def _assess_network_impact(
        self, 
        request: FirewallChangeRequest,
        validation_results: List[ValidationResult]
    ) -> str:
        """Assess potential network impact."""
        impact_factors = []
        
        # Check for broad network access
        for rule in request.rules:
            if rule.source in ['*', 'any', '0.0.0.0/0'] or rule.destination in ['*', 'any', '0.0.0.0/0']:
                impact_factors.append("Broad network access")
            
            if 'internet' in rule.destination.lower():
                impact_factors.append("External internet access")
            
            if rule.port and '-' in rule.port:
                impact_factors.append("Port range access")
        
        # Check validation results for network-related issues
        for result in validation_results:
            for violation in result.violations:
                if 'wildcard' in violation.type.value:
                    impact_factors.append("Overly permissive access")
                elif 'range' in violation.type.value:
                    impact_factors.append("Large port range")
        
        if not impact_factors:
            return "Low - Specific, targeted access"
        elif len(impact_factors) == 1:
            return f"Medium - {impact_factors[0]}"
        else:
            return f"High - Multiple factors: {', '.join(impact_factors[:3])}"
    
    def _assess_business_impact(
        self, 
        request: FirewallChangeRequest,
        risk_level: RiskLevel
    ) -> str:
        """Assess potential business impact."""
        if risk_level == RiskLevel.CRITICAL:
            return "High - Critical security risks may impact business operations"
        elif risk_level == RiskLevel.HIGH:
            return "Medium-High - Security risks require careful consideration"
        elif risk_level == RiskLevel.MEDIUM:
            return "Medium - Moderate risks with manageable impact"
        elif risk_level == RiskLevel.LOW:
            return "Low - Minor risks with minimal business impact"
        else:
            return "Minimal - No significant business impact expected"
