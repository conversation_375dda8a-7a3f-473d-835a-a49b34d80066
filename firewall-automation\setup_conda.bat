@echo off
REM Setup script for Firewall Automation using Conda (Windows)
REM This script sets up the conda environment and dependencies

echo 🚀 Setting up Firewall Automation with Conda
echo ==============================================

REM Check if conda is installed
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Conda is not installed. Please install Anaconda or Miniconda first.
    echo    Download from: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo ✅ Conda found
conda --version

REM Check if environment.yml exists
if not exist "environment.yml" (
    echo ❌ environment.yml not found. Make sure you're in the firewall-automation directory.
    pause
    exit /b 1
)

REM Create conda environment
echo 📦 Creating conda environment from environment.yml...
conda env list | findstr "firewall-automation" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⚠️  Environment 'firewall-automation' already exists.
    set /p update="Do you want to update it? (y/N): "
    if /i "%update%"=="y" (
        echo 🔄 Updating existing environment...
        conda env update -f environment.yml
    ) else (
        echo ℹ️  Skipping environment creation.
    )
) else (
    conda env create -f environment.yml
)

echo ✅ Conda environment created/updated successfully

REM Activate environment and test
echo 🧪 Testing environment setup...
call conda activate firewall-automation

REM Test Python imports
python -c "import sys; print(f'✅ Python {sys.version}')"
python -c "import requests, click, pandas, json; print('✅ Core dependencies imported successfully')"
python -c "from src.config.settings import settings; print('✅ Application modules imported successfully')"

REM Check for .env file
if not exist ".env" (
    echo ⚠️  .env file not found.
    if exist ".env.example" (
        echo 📋 Copying .env.example to .env...
        copy ".env.example" ".env"
        echo ✅ .env file created. Please edit it with your actual credentials.
    ) else (
        echo ❌ .env.example not found either. Please create .env manually.
    )
) else (
    echo ✅ .env file found
)

REM Create output directory
if not exist "output" mkdir output
if not exist "output\logs" mkdir output\logs
if not exist "output\reports" mkdir output\reports
echo ✅ Output directories created

echo.
echo 🎉 Setup completed successfully!
echo.
echo Next steps:
echo 1. Activate the environment: conda activate firewall-automation
echo 2. Edit .env file with your API credentials
echo 3. Test the setup: python test_setup.py
echo 4. Run analysis: python -m src.main --jira-issue YOUR-ISSUE-KEY --dry-run
echo.
echo For more information, see README.md
pause
