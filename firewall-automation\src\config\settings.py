"""
Configuration management for the firewall automation system.
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class JiraSettings(BaseSettings):
    """Jira API configuration settings."""
    
    url: str = Field(..., env="JIRA_URL")
    username: str = Field(..., env="JIRA_USERNAME")
    api_token: str = Field(..., env="JIRA_API_TOKEN")
    project_key: Optional[str] = Field(None, env="JIRA_PROJECT_KEY")
    
    class Config:
        env_prefix = "JIRA_"


class TufinSettings(BaseSettings):
    """Tufin API configuration settings."""
    
    url: str = Field(..., env="TUFIN_URL")
    username: str = Field(..., env="TUFIN_USERNAME")
    password: str = Field(..., env="TUFIN_PASSWORD")
    verify_ssl: bool = Field(True, env="TUFIN_VERIFY_SSL")
    timeout: int = Field(30, env="TUFIN_TIMEOUT")
    
    class Config:
        env_prefix = "TUFIN_"


class AppSettings(BaseSettings):
    """Application configuration settings."""
    
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("json", env="LOG_FORMAT")  # json or text
    output_dir: str = Field("./output", env="OUTPUT_DIR")
    security_standards_file: str = Field(
        "./src/config/security_standards.json", 
        env="SECURITY_STANDARDS_FILE"
    )
    dry_run: bool = Field(False, env="DRY_RUN")
    
    class Config:
        env_prefix = "APP_"


class Settings:
    """Main settings container."""
    
    def __init__(self):
        self.jira = JiraSettings()
        self.tufin = TufinSettings()
        self.app = AppSettings()
    
    def validate(self) -> bool:
        """Validate all required settings are present."""
        try:
            # Validate Jira settings
            assert self.jira.url, "JIRA_URL is required"
            assert self.jira.username, "JIRA_USERNAME is required"
            assert self.jira.api_token, "JIRA_API_TOKEN is required"
            
            # Validate Tufin settings
            assert self.tufin.url, "TUFIN_URL is required"
            assert self.tufin.username, "TUFIN_USERNAME is required"
            assert self.tufin.password, "TUFIN_PASSWORD is required"
            
            return True
        except AssertionError as e:
            print(f"Configuration validation failed: {e}")
            return False


# Global settings instance
settings = Settings()
