"""
Configuration management for the firewall automation system.
"""

import os
from typing import Optional
from dataclasses import dataclass

# Load environment variables from .env file if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv not available, use environment variables directly
    pass


@dataclass
class JiraSettings:
    """Jira API configuration settings."""

    def __init__(self):
        self.url = os.getenv("JIRA_URL", "")
        self.username = os.getenv("JIRA_USERNAME", "")
        self.api_token = os.getenv("JIRA_API_TOKEN", "")
        self.project_key = os.getenv("JIRA_PROJECT_KEY")


@dataclass
class TufinSettings:
    """Tufin API configuration settings."""

    def __init__(self):
        self.url = os.getenv("TUFIN_URL", "")
        self.username = os.getenv("TUFIN_USERNAME", "")
        self.password = os.getenv("TUFIN_PASSWORD", "")
        self.verify_ssl = os.getenv("TUFIN_VERIFY_SSL", "true").lower() == "true"
        self.timeout = int(os.getenv("TUFIN_TIMEOUT", "30"))


@dataclass
class AppSettings:
    """Application configuration settings."""

    def __init__(self):
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_format = os.getenv("LOG_FORMAT", "json")  # json or text
        self.output_dir = os.getenv("OUTPUT_DIR", "./output")
        self.security_standards_file = os.getenv(
            "SECURITY_STANDARDS_FILE",
            "./src/config/security_standards.json"
        )
        self.dry_run = os.getenv("DRY_RUN", "false").lower() == "true"


class Settings:
    """Main settings container."""

    def __init__(self):
        self.jira = JiraSettings()
        self.tufin = TufinSettings()
        self.app = AppSettings()

    def validate(self) -> bool:
        """Validate all required settings are present."""
        try:
            # Validate Jira settings
            assert self.jira.url, "JIRA_URL is required"
            assert self.jira.username, "JIRA_USERNAME is required"
            assert self.jira.api_token, "JIRA_API_TOKEN is required"

            # Validate Tufin settings
            assert self.tufin.url, "TUFIN_URL is required"
            assert self.tufin.username, "TUFIN_USERNAME is required"
            assert self.tufin.password, "TUFIN_PASSWORD is required"

            return True
        except AssertionError as e:
            print(f"Configuration validation failed: {e}")
            return False


# Global settings instance
settings = Settings()
