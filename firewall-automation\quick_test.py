#!/usr/bin/env python3
"""
Quick test script for rapid debugging of specific components.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "tests"))

from src.jira.parser import FirewallTableParser, FirewallRule
from src.security.validator import SecurityValidator
from tests.mock_data import TEST_SCENARIOS


def test_quick_validation():
    """Quick validation test with various rule types."""
    print("🔒 Quick Security Validation Test")
    print("="*50)
    
    validator = SecurityValidator()
    
    # Test cases with expected outcomes
    test_rules = [
        {
            'name': 'Good Rule',
            'rule': FirewallRule(
                source='*************',
                destination='*********',
                port='443',
                protocol='HTTPS',
                justification='Production web application requires secure access to backend API server for customer transactions.'
            ),
            'expected_risk': 0
        },
        {
            'name': 'Wildcard Source',
            'rule': FirewallRule(
                source='*',
                destination='*********',
                port='443',
                protocol='HTTPS',
                justification='Emergency access needed'
            ),
            'expected_risk': 10
        },
        {
            'name': 'Critical Protocol',
            'rule': FirewallRule(
                source='*************',
                destination='**********',
                port='23',
                protocol='telnet',
                justification='Legacy system access'
            ),
            'expected_risk': 10
        },
        {
            'name': 'Attack Port',
            'rule': FirewallRule(
                source='*************',
                destination='**********',
                port='31337',
                protocol='TCP',
                justification='Custom application port'
            ),
            'expected_risk': 8
        },
        {
            'name': 'Database Port',
            'rule': FirewallRule(
                source='*************',
                destination='**********',
                port='1433',
                protocol='TCP',
                justification='Application database connection required for user authentication'
            ),
            'expected_risk': 8
        },
        {
            'name': 'P2P Port',
            'rule': FirewallRule(
                source='*************',
                destination='internet',
                port='6881',
                protocol='TCP',
                justification='File sharing application'
            ),
            'expected_risk': 16  # P2P + wildcard destination
        }
    ]
    
    for test_case in test_rules:
        print(f"\n--- {test_case['name']} ---")
        result = validator.validate_rule(test_case['rule'])
        
        print(f"Risk Score: {result.total_risk_score} (expected: ~{test_case['expected_risk']})")
        print(f"Compliant: {'✅' if result.is_compliant else '❌'}")
        print(f"Violations: {len(result.violations)}")
        
        if result.violations:
            for violation in result.violations:
                print(f"  - {violation.type.value}: {violation.message} (Score: {violation.risk_score})")


def test_quick_parsing():
    """Quick parsing test with different formats."""
    print("\n📝 Quick Parsing Test")
    print("="*50)
    
    parser = FirewallTableParser()
    
    # Test different table formats
    test_issues = [
        {
            'name': 'Jira Table Format',
            'issue': {
                'key': 'TEST-001',
                'summary': 'Test Jira table',
                'description': '''
                ||Field||Value||
                |Source|*************|
                |Destination|*********|
                |Port|443|
                |Protocol|HTTPS|
                '''
            }
        },
        {
            'name': 'Markdown Format',
            'issue': {
                'key': 'TEST-002',
                'summary': 'Test markdown table',
                'description': '''
                | Field | Value |
                |-------|-------|
                | Source | ************* |
                | Destination | ********* |
                | Port | 443 |
                | Protocol | HTTPS |
                '''
            }
        },
        {
            'name': 'Field-Value Format',
            'issue': {
                'key': 'TEST-003',
                'summary': 'Test field-value pairs',
                'description': '''
                Source: *************
                Destination: *********
                Port: 443
                Protocol: HTTPS
                '''
            }
        }
    ]
    
    for test_case in test_issues:
        print(f"\n--- {test_case['name']} ---")
        request = parser.parse_issue(test_case['issue'])
        
        print(f"Rules found: {len(request.rules)}")
        print(f"Parsing errors: {len(request.parsing_errors)}")
        
        if request.parsing_errors:
            for error in request.parsing_errors:
                print(f"  Error: {error}")
        
        for i, rule in enumerate(request.rules, 1):
            print(f"  Rule {i}: {rule.source} -> {rule.destination}:{rule.port} ({rule.protocol})")


def test_risk_combinations():
    """Test various risk factor combinations."""
    print("\n📊 Risk Combination Test")
    print("="*50)
    
    validator = SecurityValidator()
    
    # Test combinations of risk factors
    combinations = [
        {
            'name': 'Multiple High Risks',
            'rule': FirewallRule(
                source='*',  # Wildcard source (10)
                destination='any',  # Wildcard destination (9)
                port='1433',  # Database port (8)
                protocol='ftp',  # High-risk protocol (8)
                justification='testing'  # Red flag justification (3)
            )
        },
        {
            'name': 'Critical + Suspicious',
            'rule': FirewallRule(
                source='any',  # Wildcard source (10)
                destination='**********',
                port='31337',  # Attack port (8)
                protocol='telnet',  # Critical protocol (10)
                justification='quick fix needed'  # Red flag (3)
            )
        },
        {
            'name': 'Legacy System Access',
            'rule': FirewallRule(
                source='***********/24',
                destination='**********',
                port='23',  # Prohibited port (10)
                protocol='telnet',  # Critical protocol (10)
                justification='Legacy system maintenance required for quarterly compliance audit'
            )
        }
    ]
    
    for test_case in combinations:
        print(f"\n--- {test_case['name']} ---")
        result = validator.validate_rule(test_case['rule'])
        
        print(f"Total Risk Score: {result.total_risk_score}")
        print(f"Violation Count: {len(result.violations)}")
        
        # Group violations by severity
        by_severity = {}
        for violation in result.violations:
            severity = violation.severity.value
            if severity not in by_severity:
                by_severity[severity] = []
            by_severity[severity].append(violation)
        
        for severity in ['critical', 'high', 'medium', 'low']:
            if severity in by_severity:
                print(f"  {severity.upper()}: {len(by_severity[severity])} violations")
                for violation in by_severity[severity]:
                    print(f"    - {violation.type.value} (Score: {violation.risk_score})")


def test_scenario_comparison():
    """Compare all predefined scenarios."""
    print("\n🔍 Scenario Comparison")
    print("="*50)
    
    parser = FirewallTableParser()
    validator = SecurityValidator()
    
    print(f"{'Scenario':<20} {'Risk Score':<12} {'Violations':<12} {'Compliant':<10}")
    print("-" * 60)
    
    for scenario_name, scenario in TEST_SCENARIOS.items():
        request = parser.parse_issue(scenario['jira_data'])
        
        if request.rules:
            result = validator.validate_rule(request.rules[0])  # Test first rule
            compliant = "✅ Yes" if result.is_compliant else "❌ No"
            
            print(f"{scenario_name:<20} {result.total_risk_score:<12} {len(result.violations):<12} {compliant:<10}")
        else:
            print(f"{scenario_name:<20} {'N/A':<12} {'N/A':<12} {'N/A':<10}")


def main():
    """Run quick tests."""
    print("🚀 Firewall Automation Quick Test")
    print("="*60)
    
    try:
        test_quick_parsing()
        test_quick_validation()
        test_risk_combinations()
        test_scenario_comparison()
        
        print("\n✅ All quick tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
