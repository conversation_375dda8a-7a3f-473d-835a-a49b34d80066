"""
Helper utilities for the firewall automation system.
"""

import re
import ipaddress
from typing import List, Tu<PERSON>, Optional, Dict, Any


def validate_ip_address(ip_str: str) -> bool:
    """
    Validate if a string is a valid IP address or network.
    
    Args:
        ip_str: IP address or network string
    
    Returns:
        True if valid, False otherwise
    """
    try:
        # Try as individual IP
        ipaddress.ip_address(ip_str)
        return True
    except ValueError:
        try:
            # Try as network
            ipaddress.ip_network(ip_str, strict=False)
            return True
        except ValueError:
            return False


def parse_port_range(port_str: str) -> Tuple[int, int]:
    """
    Parse a port string into start and end port numbers.
    
    Args:
        port_str: Port string (e.g., "80", "80-443", "1000 to 2000")
    
    Returns:
        Tuple of (start_port, end_port)
    
    Raises:
        ValueError: If port string is invalid
    """
    port_str = port_str.strip()
    
    # Single port
    if port_str.isdigit():
        port = int(port_str)
        return (port, port)
    
    # Range with dash
    if '-' in port_str:
        parts = port_str.split('-', 1)
        if len(parts) == 2 and parts[0].strip().isdigit() and parts[1].strip().isdigit():
            start = int(parts[0].strip())
            end = int(parts[1].strip())
            return (start, end)
    
    # Range with "to"
    if 'to' in port_str.lower():
        parts = port_str.lower().split('to')
        if len(parts) == 2 and parts[0].strip().isdigit() and parts[1].strip().isdigit():
            start = int(parts[0].strip())
            end = int(parts[1].strip())
            return (start, end)
    
    raise ValueError(f"Invalid port range format: {port_str}")


def format_jira_comment(
    title: str,
    recommendations: List[Dict[str, Any]],
    risk_score: int,
    timestamp: Optional[str] = None
) -> str:
    """
    Format a Jira comment with security analysis results.
    
    Args:
        title: Comment title
        recommendations: List of recommendation dictionaries
        risk_score: Overall risk score (1-10)
        timestamp: Optional timestamp string
    
    Returns:
        Formatted Jira comment text
    """
    lines = [
        f"🔒 *{title}*",
        "",
        f"📊 *Risk Score:* {risk_score}/10",
        ""
    ]
    
    if risk_score >= 8:
        lines.append("🚨 *HIGH RISK* - Immediate attention required")
    elif risk_score >= 6:
        lines.append("⚠️ *MEDIUM RISK* - Review recommended")
    elif risk_score >= 3:
        lines.append("⚡ *LOW RISK* - Minor issues identified")
    else:
        lines.append("✅ *LOW RISK* - Minimal security concerns")
    
    lines.extend(["", "🔍 *Security Analysis Results:*", ""])
    
    if not recommendations:
        lines.append("✅ No security issues identified.")
    else:
        for i, rec in enumerate(recommendations, 1):
            severity_emoji = {
                'critical': '🚨',
                'high': '⚠️',
                'medium': '⚡',
                'low': 'ℹ️',
                'info': '✅'
            }.get(rec.get('severity', 'info'), 'ℹ️')
            
            lines.extend([
                f"{i}. {severity_emoji} *{rec.get('title', 'Unknown Issue')}*",
                f"   {rec.get('description', 'No description available')}",
                ""
            ])
            
            if rec.get('action_required', False):
                lines.append(f"   ⚡ *Action Required*")
                lines.append("")
    
    lines.extend([
        "---",
        "📋 *Next Steps:*"
    ])
    
    action_items = [r for r in recommendations if r.get('action_required', False)]
    if action_items:
        lines.append("• Address high-priority security violations")
        lines.append("• Review and update firewall rule specifications")
        lines.append("• Provide additional business justification if needed")
    else:
        lines.append("• Review analysis results")
        lines.append("• Proceed with implementation if approved")
    
    lines.extend([
        "",
        "---"
    ])
    
    if timestamp:
        lines.append(f"_Analysis generated automatically on {timestamp}_")
    else:
        lines.append("_Analysis generated automatically_")
    
    return "\n".join(lines)


def normalize_protocol_name(protocol: str) -> str:
    """
    Normalize protocol name to standard format.
    
    Args:
        protocol: Protocol name
    
    Returns:
        Normalized protocol name
    """
    if not protocol:
        return ""
    
    protocol_map = {
        'tcp': 'TCP',
        'udp': 'UDP',
        'icmp': 'ICMP',
        'ssh': 'SSH',
        'http': 'HTTP',
        'https': 'HTTPS',
        'ftp': 'FTP',
        'telnet': 'Telnet',
        'smtp': 'SMTP',
        'pop3': 'POP3',
        'imap': 'IMAP'
    }
    
    return protocol_map.get(protocol.lower(), protocol.upper())


def extract_ip_addresses(text: str) -> List[str]:
    """
    Extract IP addresses from text using regex.
    
    Args:
        text: Text to search for IP addresses
    
    Returns:
        List of found IP addresses
    """
    # IPv4 pattern
    ipv4_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}(?:/[0-9]{1,2})?\b'
    
    # IPv6 pattern (simplified)
    ipv6_pattern = r'\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b'
    
    ipv4_matches = re.findall(ipv4_pattern, text)
    ipv6_matches = re.findall(ipv6_pattern, text)
    
    return ipv4_matches + ipv6_matches


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing invalid characters.
    
    Args:
        filename: Original filename
    
    Returns:
        Sanitized filename
    """
    # Remove invalid characters
    invalid_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(invalid_chars, '_', filename)
    
    # Remove multiple underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    
    return sanitized


def calculate_network_overlap(network1: str, network2: str) -> bool:
    """
    Check if two networks overlap.
    
    Args:
        network1: First network (CIDR notation)
        network2: Second network (CIDR notation)
    
    Returns:
        True if networks overlap, False otherwise
    """
    try:
        net1 = ipaddress.ip_network(network1, strict=False)
        net2 = ipaddress.ip_network(network2, strict=False)
        
        return net1.overlaps(net2)
    except ValueError:
        return False


def format_risk_score_description(score: int) -> str:
    """
    Get a description for a risk score.
    
    Args:
        score: Risk score (1-10)
    
    Returns:
        Risk score description
    """
    if score >= 9:
        return "Critical Risk - Immediate action required"
    elif score >= 7:
        return "High Risk - Prompt attention needed"
    elif score >= 5:
        return "Medium Risk - Review recommended"
    elif score >= 3:
        return "Low Risk - Minor concerns"
    else:
        return "Minimal Risk - No significant issues"
