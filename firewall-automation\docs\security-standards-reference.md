# Security Standards Reference Guide

This document provides a comprehensive reference for the security standards used in the firewall change automation system.

## 🎯 Overview

The security standards are designed to identify and score various risk factors in firewall rule changes based on industry best practices, compliance requirements, and common attack patterns.

## 📊 Risk Scoring Scale

- **1-2**: Minimal Risk - No significant security concerns
- **3-4**: Low Risk - Minor issues that should be addressed
- **5-6**: Medium Risk - Moderate security concerns requiring review
- **7-8**: High Risk - Significant security issues requiring immediate attention
- **9-10**: Critical Risk - Severe security violations requiring immediate remediation

## 🚫 Prohibited Configurations

### Source Address Violations
- **Wildcards**: `*`, `any`, `0.0.0.0/0`, `::/0`, `all`, `internet`
- **Untrusted Networks**: External, DMZ, guest networks
- **Risk Score**: 10 (Critical)

### Destination Address Violations
- **Wildcards**: Same as source wildcards
- **Critical Infrastructure**: Domain controllers, DNS servers, LDAP servers
- **Risk Score**: 9-10 (Critical to High)

## 🔒 Protocol Risk Categories

### Critical Risk Protocols (Score: 10)
- **Telnet** - Unencrypted remote access
- **RSH/RLogin/RExec** - Legacy remote shell protocols
- **TFTP** - Trivial File Transfer Protocol (no authentication)
- **Finger** - User information protocol
- **Echo/Chargen** - Amplification attack vectors

### High Risk Protocols (Score: 8)
- **FTP** - Unencrypted file transfer
- **SNMP v1/v2** - Unencrypted network management
- **NetBIOS/SMB/CIFS** - Windows file sharing protocols
- **NFS** - Network File System
- **X11** - X Window System

### Medium Risk Protocols (Score: 5)
- **SSH** - Secure but commonly targeted
- **HTTP** - Unencrypted web traffic
- **SMTP/POP3/IMAP** - Email protocols
- **LDAP** - Directory access protocol
- **Kerberos/RADIUS** - Authentication protocols

### Legacy Protocols (Score: 7)
- **AppleTalk** - Legacy Apple networking
- **IPX** - Legacy Novell protocol
- **NetBEUI** - Legacy Microsoft protocol
- **DECnet** - Legacy Digital Equipment protocol

## 🔌 Port Risk Categories

### Prohibited Ports (Score: 10)
- **23** - Telnet
- **69** - TFTP
- **79** - Finger
- **7, 9, 11, 13, 15, 17, 19** - Echo services
- **512, 513, 514, 515** - R-services
- **540** - UUCP

### Critical Infrastructure Ports (Score: 9)
- **53** - DNS
- **88** - Kerberos
- **135** - RPC Endpoint Mapper
- **139** - NetBIOS Session Service
- **389** - LDAP
- **445** - SMB
- **464** - Kerberos Password Change
- **636** - LDAPS
- **3268, 3269** - Global Catalog

### Database Ports (Score: 8)
- **1433, 1434** - Microsoft SQL Server
- **1521, 1522** - Oracle
- **3306** - MySQL
- **5432** - PostgreSQL
- **1830** - Oracle Listener
- **50000, 60000** - DB2

### Management Ports (Score: 7)
- **161, 162** - SNMP
- **623** - IPMI
- **664** - Secure ASAP
- **8080, 8443** - HTTP/HTTPS Alternate
- **9090** - Management interfaces

### Remote Access Ports (Score: 6)
- **3389** - RDP
- **5900-5902** - VNC
- **22** - SSH
- **992** - Telnet over SSL
- **5985, 5986** - WinRM

## 🚨 Suspicious Activity Patterns

### Common Attack Ports (Score: 8)
- **4444** - Metasploit default
- **31337** - Elite/Leet (common backdoor)
- **12345, 54321** - NetBus trojan
- **9999** - Various trojans
- **6666** - IRC/Trojan
- **1234** - Ultors trojan

### P2P Application Ports (Score: 7)
- **6881-6889** - BitTorrent
- **4662** - eMule
- **1214** - Kazaa
- **6346** - Gnutella

### Tor Network Ports (Score: 8)
- **9001** - Tor relay
- **9030** - Tor directory
- **9050, 9051** - Tor SOCKS proxy
- **9150, 9151** - Tor Browser

### Cryptocurrency Ports (Score: 8)
- **8332, 8333** - Bitcoin
- **18332, 18333** - Bitcoin testnet
- **9332, 9333** - Litecoin

## 📏 Port Range Restrictions

### Maximum Range Size
- **Limit**: 50 ports per range
- **Risk Score**: 6 for oversized ranges

### Prohibited Ranges
- **1-65535** - All ports
- **0-65535** - All ports including reserved
- **1-1024** - All well-known ports
- **1024-65535** - All registered/dynamic ports

### Suspicious Ranges (Score: 5)
- **1-1000** - Too many well-known ports
- **8000-9000** - Common web application range
- **31000-32000** - High port range often used maliciously

## 📝 Justification Quality Assessment

### Required Elements
- **Minimum Length**: 20 characters
- **Business Purpose**: Clear business need
- **Approval Reference**: Change approval number
- **Duration**: Temporary vs. permanent

### Red Flag Keywords (Score: 3)
- **testing**, **temporary**, **quick_fix**
- **workaround**, **bypass**, **troubleshooting**
- **debug**, **personal**, **convenience**
- **easy_access**

### Acceptable Keywords
- **business_requirement**, **approved_by**
- **migration**, **maintenance_window**
- **backup_solution**, **monitoring**
- **integration**, **vendor_access**
- **application_requirement**, **database_connection**

### Emergency Keywords (Requires Review)
- **emergency**, **urgent**, **asap**
- **immediate**, **critical**, **outage**
- **production_down**, **customer_impact**

## 🏢 Compliance Framework Checks

### PCI DSS Requirements
- **Prohibited Protocols**: Telnet, FTP, HTTP, SNMPv1/v2
- **Required Encryption**: SSH, HTTPS, SFTP, SNMPv3
- **Risk Score**: 8 for violations

### HIPAA Requirements
- **Encryption Required**: All data transmission
- **Audit Logging**: All access must be logged
- **Risk Score**: 8 for violations

### SOX Requirements
- **Segregation Required**: Financial systems isolation
- **Change Approval**: All changes must be approved
- **Risk Score**: 7 for violations

## 🌐 Network Security Patterns

### RFC1918 External Routing (Score: 8)
- **10.0.0.0/8** - Class A private
- ************/12** - Class B private
- *************/16** - Class C private

### Multicast/Broadcast (Score: 6)
- ***********/4** - Multicast range
- ******************* - Broadcast
- **0.0.0.0** - Any address

### Loopback Addresses
- ***********/8** - IPv4 loopback
- **::1/128** - IPv6 loopback

## ⏰ Time-Based Risk Factors

### High-Risk Hours
- **18:00 - 06:00** - Outside business hours
- **Risk Multiplier**: 1.2x

### Preferred Maintenance Windows
- **Days**: Saturday, Sunday
- **Hours**: 02:00 - 04:00
- **Risk Multiplier**: 0.8x

## 🏗️ Network Zone Risk Multipliers

- **Internal**: 1.0x (baseline)
- **DMZ**: 1.5x (higher exposure)
- **External**: 2.0x (highest risk)
- **Management**: 1.8x (critical systems)

## 🎯 Action Type Risk Multipliers

- **Allow**: 1.0x (standard risk)
- **Deny**: 0.5x (safer - blocking traffic)
- **Log**: 0.3x (monitoring only)
- **NAT**: 1.3x (additional complexity)

## 🔧 Customization Guidelines

### Adding New Risk Categories
1. Update `security_standards.json`
2. Add corresponding `ViolationType` enum
3. Implement validation logic
4. Add test cases
5. Update documentation

### Adjusting Risk Scores
- Consider organizational risk tolerance
- Align with compliance requirements
- Review industry best practices
- Test with sample data

### Regular Updates
- Review quarterly for new threats
- Update based on incident analysis
- Incorporate security team feedback
- Align with policy changes
