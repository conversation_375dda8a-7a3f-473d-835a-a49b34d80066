name: firewall-automation
channels:
  - conda-forge
  - defaults
dependencies:
  # Python
  - python=3.11
  
  # Core dependencies
  - requests>=2.28.0
  - click>=8.0.0
  - urllib3>=1.26.0
  
  # Data processing
  - pandas>=1.5.0
  - jsonschema>=4.0.0
  
  # Logging and monitoring
  - colorama>=0.4.4
  
  # Testing
  - pytest>=7.0.0
  - pytest-mock>=3.10.0
  - pytest-cov>=4.0.0
  
  # Development tools
  - black>=22.0.0
  - flake8>=5.0.0
  
  # Additional useful packages
  - ipython
  - jupyter
  - pip
  
  # Pip dependencies (if needed)
  - pip:
    - python-dotenv  # Only if not available via conda
